#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : asset_package_field_config_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/07/10
@File_Desc: 资产包字段配置视图集
"""

from django.db import transaction
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend

from apps.data_governance.models import AssetPackageFieldConfig
from apps.data_governance.serializers import (
    AssetPackageFieldConfigListSerializer,
    AssetPackageFieldConfigCreateUpdateSerializer
)
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult


class BaseAssetPackageFieldConfigViewSet(viewsets.ModelViewSet):
    """
    资产包字段配置基础视图集

    提供资产包字段配置管理的基础功能配置，包括查询集、序列化器、分页和过滤配置。
    该基础类定义了标准字段配置的通用操作规范和数据访问模式。
    """
    queryset = AssetPackageFieldConfig.objects.all().order_by("display_order")
    serializer_class = AssetPackageFieldConfigListSerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["field_type", "data_validation", "is_masked"]
    search_fields = ["field_name"]


class AssetPackageFieldConfigViewSet(BaseAssetPackageFieldConfigViewSet):
    """
    资产包字段配置管理视图集

    提供资产包字段配置的完整管理功能，包括标准CRUD操作和自定义业务功能。
    该视图集管理系统中所有标准字段配置，为Excel文件字段映射提供基础数据支持。
    """

    def get_serializer_class(self):
        """
        根据不同的操作动态选择合适的序列化器
        """
        if self.action in ['create', 'update', 'partial_update']:
            return AssetPackageFieldConfigCreateUpdateSerializer
        else:
            return AssetPackageFieldConfigListSerializer
    
    def list(self, request, *args, **kwargs):
        """
        获取资产包字段配置列表

        获取系统中所有标准字段配置的分页列表，支持多维度过滤和搜索功能。
        该接口为Excel字段映射和数据验证规则配置提供基础数据支持。

        **请求参数：**
        - field_type (字符串, 可选): 字段类型过滤，可选值：text(文本类型)、date(日期类型)、numeric(数值类型)、amount(金额类型)
        - data_validation (字符串, 可选): 数据验证规则过滤，可选值：none(无校验)、phone(手机号格式校验)、id_card(身份证格式校验)、social_credit_code(统一社会信用代码校验)
        - is_masked (布尔值, 可选): 脱敏状态过滤，可选值：true(启用脱敏)、false(不脱敏)
        - search (字符串, 可选): 按字段名称进行模糊搜索的关键词
        - page (整数, 可选): 页码，从1开始，默认为1
        - page_size (整数, 可选): 每页记录数，默认20，最大100

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "count": 100,
                "next": "http://example.com/api/next_page",
                "previous": "http://example.com/api/previous_page",
                "results": [
                    {
                        "id": 1,
                        "field_name": "客户姓名",
                        "field_type": "text",
                        "field_type_cn": "文本类型",
                        "data_validation": "none",
                        "data_validation_cn": "无校验",
                        "is_masked": true,
                        "prefix_keep_chars": 1,
                        "suffix_keep_chars": 1,
                        "display_order": 100
                    },
                    {
                        "id": 2,
                        "field_name": "联系电话",
                        "field_type": "text",
                        "field_type_cn": "文本类型",
                        "data_validation": "phone",
                        "data_validation_cn": "手机号格式校验",
                        "is_masked": true,
                        "prefix_keep_chars": 3,
                        "suffix_keep_chars": 4,
                        "display_order": 200
                    }
                ]
            }
        }
        ```
        """
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        """
        创建新的资产包字段配置

        创建一个新的标准字段配置，用于Excel文件字段映射和数据验证。
        该接口为系统添加新的字段类型和验证规则，支持数据脱敏配置。

        **请求参数：**
        - field_name (字符串, 必需): 字段名称，在系统中必须唯一，最大长度100字符
        - field_type (字符串, 必需): 字段类型，可选值：text(文本类型)、date(日期类型)、numeric(数值类型)、amount(金额类型)
        - data_validation (字符串, 可选): 数据验证规则，可选值：none(无校验)、phone(手机号格式校验)、id_card(身份证格式校验)、social_credit_code(统一社会信用代码校验)，默认为none
        - is_masked (布尔值, 可选): 是否启用数据脱敏，默认为false
        - prefix_keep_chars (整数, 可选): 脱敏时前保留字符数，默认为0，必须为非负整数
        - suffix_keep_chars (整数, 可选): 脱敏时后保留字符数，默认为0，必须为非负整数
        - display_order (整数, 可选): 显示顺序，用于前端排序，默认为100，必须为正整数

        **请求数据示例：**
        ```json
        {
            "field_name": "客户手机号",
            "field_type": "text",
            "data_validation": "phone",
            "is_masked": true,
            "prefix_keep_chars": 3,
            "suffix_keep_chars": 4,
            "display_order": 200
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "新增成功",
            "data": null
        }
        ```
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():
            serializer.save()
        return AjaxResult.success(msg="新增成功")

    def retrieve(self, request, *args, **kwargs):
        """
        获取资产包字段配置详情

        根据字段配置ID获取单个字段配置的完整详细信息。
        该接口用于字段配置的详情查看和编辑前的数据加载。

        **请求参数：**
        - id (整数, 必需): 字段配置的唯一标识ID，通过URL路径传递

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "id": 1,
                "field_name": "客户姓名",
                "field_type": "text",
                "field_type_cn": "文本类型",
                "data_validation": "none",
                "data_validation_cn": "无校验",
                "is_masked": true,
                "prefix_keep_chars": 1,
                "suffix_keep_chars": 1,
                "display_order": 100
            }
        }
        ```
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)

    def update(self, request, *args, **kwargs):
        """
        更新资产包字段配置

        更新现有字段配置的完整信息，支持修改字段属性、验证规则和脱敏配置。
        该接口仅支持完整更新，确保数据的一致性和完整性。

        **请求参数：**
        - id (整数, 必需): 字段配置的唯一标识ID，通过URL路径传递
        - field_name (字符串, 可选): 字段名称，如果修改必须保持系统唯一性
        - field_type (字符串, 可选): 字段类型，可选值：text(文本类型)、date(日期类型)、numeric(数值类型)、amount(金额类型)
        - data_validation (字符串, 可选): 数据验证规则，可选值：none(无校验)、phone(手机号格式校验)、id_card(身份证格式校验)、social_credit_code(统一社会信用代码校验)
        - is_masked (布尔值, 可选): 是否启用数据脱敏
        - prefix_keep_chars (整数, 可选): 脱敏时前保留字符数，必须为非负整数
        - suffix_keep_chars (整数, 可选): 脱敏时后保留字符数，必须为非负整数
        - display_order (整数, 可选): 显示顺序，必须为正整数

        **请求数据示例：**
        ```json
        {
            "field_name": "客户手机号码",
            "field_type": "text",
            "data_validation": "phone",
            "is_masked": true,
            "prefix_keep_chars": 3,
            "suffix_keep_chars": 4,
            "display_order": 150
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "编辑成功",
            "data": null
        }
        ```
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():
            serializer.save()
        return AjaxResult.success(msg="编辑成功")

    def partial_update(self, request, *args, **kwargs):
        """
        禁用部分更新操作

        系统不支持PATCH方法的部分更新操作，以确保数据完整性和业务逻辑一致性。
        请使用PUT方法进行完整更新操作。

        **请求参数：**
        - 任何PATCH请求参数都将被拒绝

        **响应数据结构：**
        ```json
        {
            "code": 405,
            "msg": "不支持部分更新操作，请使用PUT方法进行完整更新",
            "data": null
        }
        ```
        """
        from rest_framework.response import Response
        from rest_framework import status

        return Response(
            {
                "code": 405,
                "msg": "不支持部分更新操作，请使用PUT方法进行完整更新",
                "data": None
            },
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )

    def destroy(self, request, *args, **kwargs):
        """
        删除资产包字段配置

        删除指定的字段配置记录，该操作会影响所有使用该配置的字段映射关系。
        删除操作不可逆，请谨慎使用。

        **请求参数：**
        - id (整数, 必需): 字段配置的唯一标识ID，通过URL路径传递

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "删除成功",
            "data": null
        }
        ```
        """
        instance = self.get_object()
        try:
            with transaction.atomic():
                instance.delete()
            return AjaxResult.success(msg="删除成功")
        except Exception as e:
            return AjaxResult.fail(msg=f"删除失败：{str(e)}")
    
    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """
        批量更新资产包字段配置

        支持一次性更新多个字段配置，提高批量操作效率。
        每个配置项独立处理，部分失败不影响其他配置的更新。

        **请求参数：**
        - items (数组, 必需): 配置项数组，每个配置项包含：
          - id (整数, 必需): 字段配置ID
          - field_name (字符串, 可选): 字段名称
          - field_type (字符串, 可选): 字段类型，可选值：text、date、numeric、amount
          - data_validation (字符串, 可选): 数据验证规则，可选值：none、phone、id_card、social_credit_code
          - is_masked (布尔值, 可选): 是否启用脱敏
          - prefix_keep_chars (整数, 可选): 前保留字符数
          - suffix_keep_chars (整数, 可选): 后保留字符数
          - display_order (整数, 可选): 显示顺序

        **请求数据示例：**
        ```json
        {
            "items": [
                {
                    "id": 1,
                    "field_name": "客户姓名",
                    "is_masked": true,
                    "prefix_keep_chars": 1,
                    "suffix_keep_chars": 1
                },
                {
                    "id": 2,
                    "field_type": "date",
                    "data_validation": "none",
                    "display_order": 200
                }
            ]
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "批量更新成功，共更新2条记录",
            "data": {
                "updated_count": 2,
                "updated_items": [
                    {"id": 1, "success": true},
                    {"id": 2, "success": true}
                ],
                "error_count": 0,
                "errors": []
            }
        }
        ```
        """
        items = request.data.get('items', [])
        if not items:
            return AjaxResult.fail(msg="批量更新数据不能为空")
        
        if not isinstance(items, list):
            return AjaxResult.fail(msg="items字段必须是数组格式")
        
        updated_items = []
        errors = []
        
        # 分批处理，避免事务过大
        for item_data in items:
            item_id = item_data.get('id')
            if not item_id:
                errors.append({"item": item_data, "error": "缺少必需的id字段"})
                continue
            
            try:
                with transaction.atomic():
                    instance = AssetPackageFieldConfig.objects.get(id=item_id)
                    serializer = AssetPackageFieldConfigCreateUpdateSerializer(
                        instance, data=item_data, partial=True
                    )
                    if serializer.is_valid():
                        serializer.save()
                        updated_items.append({"id": item_id, "success": True})
                    else:
                        errors.append({"id": item_id, "error": serializer.errors})
            except AssetPackageFieldConfig.DoesNotExist:
                errors.append({"id": item_id, "error": "记录不存在"})
            except Exception as e:
                errors.append({"id": item_id, "error": str(e)})
        
        result_data = {
            "updated_count": len(updated_items),
            "updated_items": updated_items,
            "error_count": len(errors),
            "errors": errors
        }
        
        if errors and not updated_items:
            return AjaxResult.fail(data=result_data, msg="批量更新失败，所有记录都更新失败")
        elif errors:
            return AjaxResult.success(data=result_data, msg=f"批量更新部分完成，成功更新{len(updated_items)}条记录，{len(errors)}条记录更新失败")
        else:
            return AjaxResult.success(data=result_data, msg=f"批量更新成功，共更新{len(updated_items)}条记录")
    
    @action(detail=False, methods=['get'])
    def field_types(self, request):
        """
        获取字段类型和验证规则选项

        提供系统支持的所有字段类型和数据验证规则选项。
        该接口用于前端表单的下拉选择和配置界面的选项展示。

        **请求参数：**
        - 无需任何参数

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "field_types": {
                    "text": "文本类型",
                    "date": "日期类型",
                    "numeric": "数值类型",
                    "amount": "金额类型"
                },
                "validation_types": {
                    "none": "无校验",
                    "phone": "手机号格式校验",
                    "id_card": "身份证格式校验",
                    "social_credit_code": "统一社会信用代码校验"
                }
            }
        }
        ```
        """
        data = {
            'field_types': dict(AssetPackageFieldConfig.FIELD_TYPE_CHOICES),
            'validation_types': dict(AssetPackageFieldConfig.VALIDATION_CHOICES),
        }

        return AjaxResult.success(data=data)
