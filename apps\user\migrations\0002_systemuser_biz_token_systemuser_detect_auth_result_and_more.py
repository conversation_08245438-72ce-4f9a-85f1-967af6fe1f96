# Generated by Django 4.1.13 on 2025-07-26 15:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='systemuser',
            name='biz_token',
            field=models.CharField(blank=True, help_text='人脸核身流程的唯一业务标识，用于追踪核身流程', max_length=255, null=True, verbose_name='人脸核身业务标识'),
        ),
        migrations.AddField(
            model_name='systemuser',
            name='detect_auth_result',
            field=models.BooleanField(blank=True, default=False, help_text='人脸核身结果：True表示核身成功，False表示核身失败', null=True, verbose_name='人脸核身结果'),
        ),
        migrations.AddField(
            model_name='systemuser',
            name='detect_auth_time',
            field=models.DateTimeField(blank=True, help_text='人脸核身完成的日期时间', null=True, verbose_name='人脸核身时间'),
        ),
        migrations.AddField(
            model_name='systemuser',
            name='real_name',
            field=models.Char<PERSON>ield(blank=True, help_text='用户的真实姓名，通过人脸核身验证获得', max_length=100, null=True, verbose_name='真实姓名'),
        ),
        migrations.AddField(
            model_name='systemuser',
            name='wechat_avatar_url',
            field=models.URLField(blank=True, null=True, verbose_name='微信头像URL'),
        ),
        migrations.AddField(
            model_name='systemuser',
            name='wechat_bind_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='微信绑定时间'),
        ),
        migrations.AddField(
            model_name='systemuser',
            name='wechat_nickname',
            field=models.CharField(blank=True, max_length=64, null=True, verbose_name='微信昵称'),
        ),
        migrations.AddField(
            model_name='systemuser',
            name='wechat_openid',
            field=models.CharField(blank=True, help_text='微信小程序用户唯一标识', max_length=64, null=True, unique=True, verbose_name='微信OpenID'),
        ),
        migrations.AddField(
            model_name='systemuser',
            name='wechat_session_key',
            field=models.CharField(blank=True, help_text='微信会话密钥(加密存储)', max_length=128, null=True, verbose_name='微信SessionKey'),
        ),
        migrations.AddField(
            model_name='systemuser',
            name='wechat_unionid',
            field=models.CharField(blank=True, help_text='微信开放平台用户唯一标识', max_length=64, null=True, verbose_name='微信UnionID'),
        ),
    ]
