# Generated by Django 4.1.13 on 2025-07-31 21:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mediation_management', '0004_mediationcasefile_secure_token'),
    ]

    operations = [
        migrations.AddField(
            model_name='mediationcase',
            name='confirmed_mediation_config',
            field=models.JSONField(blank=True, help_text='用户确认调解方案时从关联资产包获取的调解配置信息快照，JSON格式', null=True, verbose_name='被确认的调解信息配置'),
        ),
        migrations.AddField(
            model_name='mediationcase',
            name='confirmed_plan_config',
            field=models.JSONField(blank=True, help_text='用户确认调解方案时从关联调解方案获取的方案配置信息快照，JSON格式', null=True, verbose_name='被确认的调解方案配置'),
        ),
    ]
