#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : asset_package_field_config_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/07/10
@File_Desc: 资产包字段配置序列化器 - 重构后移动到serializers包中
"""

from rest_framework import serializers
from apps.data_governance.models import AssetPackageFieldConfig


class AssetPackageFieldConfigListSerializer(serializers.ModelSerializer):
    """资产包字段配置列表序列化器"""
    
    field_type_cn = serializers.CharField(source='get_field_type_display', read_only=True)
    data_validation_cn = serializers.CharField(source='get_data_validation_display', read_only=True)
    
    class Meta:
        model = AssetPackageFieldConfig
        fields = ['id', 'field_name', 'field_type', 'field_type_cn', 'data_validation', 
                 'data_validation_cn', 'is_masked', 'prefix_keep_chars', 'suffix_keep_chars', 'display_order']


class AssetPackageFieldConfigCreateUpdateSerializer(serializers.ModelSerializer):
    """资产包字段配置创建更新序列化器"""
    
    class Meta:
        model = AssetPackageFieldConfig
        fields = ['field_name', 'field_type', 'data_validation', 'is_masked', 
                 'prefix_keep_chars', 'suffix_keep_chars', 'display_order']
    
    def validate(self, data):
        """全局验证"""
        is_masked = data.get('is_masked', False)
        prefix_keep_chars = data.get('prefix_keep_chars', 0)
        suffix_keep_chars = data.get('suffix_keep_chars', 0)
        
        # 当启用脱敏时，前后保留字符数不能同时为0
        if is_masked and prefix_keep_chars == 0 and suffix_keep_chars == 0:
            raise serializers.ValidationError("启用脱敏时，前保留字符数和后保留字符数不能同时为0")
        
        return data
    
    def validate_prefix_keep_chars(self, value):
        """验证前保留字符数"""
        if value is not None and value < 0:
            raise serializers.ValidationError("前保留字符数不能为负数")
        return value
    
    def validate_suffix_keep_chars(self, value):
        """验证后保留字符数"""
        if value is not None and value < 0:
            raise serializers.ValidationError("后保留字符数不能为负数")
        return value 