#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : decorator.py
<AUTHOR> JT_DA
@Date     : 2025/06/27
@File_Desc: 
"""

from functools import wraps
from utils.ajax_result import AjaxResult


def singleton(cls):
    """
    返回单例对象的装饰器函数

    :param cls: 目标类
    :return: 装饰后的类
    """
    instances = {}

    def wrapper(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return wrapper


def singleton_para(cls):
    """
    带参数的单例装饰器

    :param cls: 目标类
    :return: 装饰后的类
    """
    instances = {}

    def wrapper(*args, **kwargs):
        key = (cls, args, frozenset(kwargs.items()))
        if key not in instances:
            instances[key] = cls(*args, **kwargs)
        return instances[key]

    return wrapper


def user_check(input_functions):
    """
    user_pass_test的替代，用于类中的函数
    """

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(object, request, *args, **kwargs):
            # 在这里实现你的用户判断逻辑，通过调用输入的判断函数进行判断
            for input_function in input_functions:
                if input_function(request):
                    return view_func(object, request, *args, **kwargs)
            else:
                return AjaxResult.forbidden(
                    "You don't have permission to access this view."
                )

        return _wrapped_view

    return decorator
