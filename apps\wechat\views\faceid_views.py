#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : faceid_views.py
<AUTHOR> JT_DA
@Date     : 2025/07/24
@File_Desc: 腾讯云FaceID人脸核身相关视图，实现身份认证功能
"""

import logging
from rest_framework.generics import GenericAPIView
from rest_framework import serializers
from rest_framework.permissions import AllowAny

from utils.ajax_result import AjaxResult
from utils.tencent_faceid_utils import detect_auth
from apps.wechat.serializers import FaceIDAuthSerializer
from apps.wechat.tasks import process_face_auth_result_async

# 获取日志记录器
logger = logging.getLogger(__name__)


class FaceIDAuthView(GenericAPIView):
    """
    腾讯云FaceID人脸核身认证视图
    
    对接腾讯云FaceID服务，提供身份认证功能。用户提供身份证号码和姓名，
    系统调用腾讯云API获取认证URL，用户通过该URL进行人脸识别验证。
    主要用于实名认证、身份核验等业务场景。
    """
    
    authentication_classes = []
    permission_classes = [AllowAny]
    serializer_class = FaceIDAuthSerializer
    
    def post(self, request):
        """
        处理人脸核身认证请求
        
        接收用户的身份证号码和姓名，调用腾讯云FaceID API获取认证URL。
        验证请求参数后，将认证信息发送给腾讯云服务，并返回认证结果。
        
        **请求参数：**
        **请求体参数：**
        - openid (字符串, 必需): 微信小程序用户唯一标识符，用于用户身份关联（暂不传递给腾讯云API）
        - id_card (字符串, 必需): 18位身份证号码，必须符合中国大陆身份证格式规范
        - name (字符串, 必需): 真实姓名，长度在2-50个字符之间，不能包含数字

        **请求数据示例：**
        ```json
        {
            "openid": "oGZUI0egBJY1zhBYw2KhdUfwVJJE",
            "id_card": "110101199001011234",
            "name": "张三"
        }
        ```
        
        **成功响应示例：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "state": "success",
            "data": "https://open.weixin.qq.com/connect/oauth2/authorize?..."
        }
        ```
        
        **失败响应示例：**
        ```json
        {
            "code": 400,
            "msg": "身份证号码格式不正确，请检查输入",
            "state": "fail",
            "data": null
        }
        ```
        
        **腾讯云API错误响应示例：**
        ```json
        {
            "code": 400,
            "msg": "RuleId不存在，请到人脸核身控制台申请。",
            "state": "fail",
            "data": null
        }
        ```
        """
        # 使用序列化器验证请求数据
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as e:
            # 记录验证失败日志
            logger.warning(f"FaceID认证请求参数验证失败: {e}")
            # 提取第一个错误信息返回给用户
            error_msg = "参数验证失败"
            if hasattr(e, "detail"):
                if isinstance(e.detail, dict):
                    # 获取第一个字段的第一个错误信息
                    for field_errors in e.detail.values():
                        if isinstance(field_errors, list) and field_errors:
                            error_msg = str(field_errors[0])
                            break
                elif isinstance(e.detail, list) and e.detail:
                    error_msg = str(e.detail[0])
            return AjaxResult.fail(msg=error_msg)
        
        # 获取验证通过的数据
        validated_data = serializer.validated_data
        openid = validated_data["openid"]
        id_card = validated_data["id_card"]
        name = validated_data["name"]

        try:
            # 调用腾讯云FaceID身份认证接口
            # 注意：openid参数暂不传递给腾讯云API，为后续功能扩展预留
            logger.info(f"开始调用腾讯云FaceID认证，openid: {openid[:8]}****{openid[-4:] if len(openid) > 12 else '****'}, 身份证号: {id_card[:6]}****{id_card[-4:]}, 姓名: {name}")
            response_data = detect_auth(id_card, name)
            
            # 记录API调用日志
            logger.info(f"腾讯云FaceID API调用完成，响应数据: {response_data}")
            
            # 检查响应格式和内容
            if not isinstance(response_data, dict) or "Response" not in response_data:
                logger.error(f"腾讯云FaceID API返回格式异常: {response_data}")
                return AjaxResult.fail(msg="服务器响应格式异常，请稍后重试")
            
            response = response_data["Response"]
            
            # 检查是否有错误信息
            if "Error" in response:
                # API返回错误响应
                error_info = response["Error"]
                error_message = error_info.get("Message", "未知错误")
                logger.warning(f"腾讯云FaceID API返回错误: {error_info}")
                return AjaxResult.fail(msg=error_message)
            
            # 检查成功响应是否包含必要字段
            if "Url" not in response:
                logger.error(f"腾讯云FaceID API成功响应缺少Url字段: {response}")
                return AjaxResult.fail(msg="认证服务响应异常，请稍后重试")

            # 成功响应，获取认证URL
            auth_url = response["Url"]
            logger.info(f"FaceID认证URL获取成功，openid: {openid[:8]}****{openid[-4:] if len(openid) > 12 else '****'}, 用户: {name}")

            # 异步处理人脸核身结果（如果存在BizToken）
            if "BizToken" in response:
                biz_token = response["BizToken"]
                auth_token = request.headers.get("Authorization", "")

                logger.info(f"检测到BizToken，启动异步处理任务，biz_token: {biz_token}")

                try:
                    # 异步调用处理任务，不阻塞主流程
                    process_face_auth_result_async.delay(auth_token, biz_token)
                    logger.info(f"异步处理任务已启动，biz_token: {biz_token}")
                except Exception as e:
                    # 异步任务启动失败不影响主流程，仅记录日志
                    logger.warning(f"启动异步处理任务失败，biz_token: {biz_token}, 错误: {str(e)}")
            else:
                logger.info("腾讯云响应中未包含BizToken，跳过异步处理")

            # 立即返回认证URL给前端，不等待异步任务完成
            return AjaxResult.success(data=auth_url)
            
        except ValueError as e:
            # 配置参数错误
            logger.error(f"腾讯云FaceID配置错误: {str(e)}")
            return AjaxResult.fail(msg="服务配置异常，请联系管理员")
        except Exception as e:
            # 其他异常处理
            logger.error(f"FaceID认证过程中发生异常: {str(e)}")
            return AjaxResult.server_error(msg="服务器内部错误，请联系管理员")
