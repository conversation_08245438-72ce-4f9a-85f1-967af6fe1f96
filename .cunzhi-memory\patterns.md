# 常用模式和最佳实践

- 数据验证功能完善模式：在utils\validator_helper.py中实现验证函数，在模型VALIDATION_CHOICES中定义选项，在ops_management\constant.py中建立映射关系，使用id-validator库进行身份证验证，添加中文注释说明
- AssetPackageManagement模型增强模式：添加status和unavailable_reason字段，支持mapped_field_config_id为null值，实现基于ops_management/constant.py中VALIDATION_FUNCTION_MAPPING的数据验证功能，根据验证结果自动更新状态
- reprocess_excel接口增强模式：支持重新上传Excel文件，接收file和reset_mappings参数，根据reset_mappings控制是否重置字段映射关系，复用现有验证逻辑，执行数据验证并更新状态，返回详细的处理结果信息
- Django调解方案管理模块开发模式：参考asset_package_management_view_set.py实现，创建三种序列化器（List/Create/Update），视图集继承ModelViewSet并根据action选择序列化器，使用详细的docstring注释格式，禁用PATCH方法，添加status_choices自定义action，支持JSON字段处理和验证，使用事务确保数据一致性，返回AjaxResult格式响应，添加中文注释说明关键逻辑
- 调解案件管理模块开发模式：基于MediationCase模型创建完整的序列化器和视图集，包含三种基础序列化器（List/Create/Update）和三种专用更新序列化器（Mediator/Plan/Signature），视图集继承ModelViewSet并根据action选择序列化器，实现自定义action接口，排除BaseModel字段和只读字段，使用事务确保数据一致性，禁用PATCH方法，添加详细的docstring注释，保持项目代码风格一致性
- MediationCaseUpdateSerializer修复模式：添加file_id字段支持附件ID列表，重构update方法实现嵌套关系的新增、修改、删除操作，添加关联对象存在性验证，提取公共验证方法消除重复代码，使用事务确保数据一致性，遵循项目标准更新操作模式
- pandas DataFrame日期格式标准化模式：在utils/date_helper.py中实现format_dataframe_dates()函数，自动识别DataFrame中的datetime64类型列，统一格式化为"YYYY-MM-DD"字符串格式，处理NaT值转换为空字符串，异常安全设计保持原值，在数据预览视图中调用确保前端日期显示一致性
- 微信认证视图开发模式：创建wechat_auth_views.py文件，实现WechatLoginView（不需要Authorization头，接收js_code/nickname/avatar_url参数）和WechatRefreshView（需要Authorization头，无需额外参数刷新session_key），使用requests库调用外部认证服务器，包含完整异常处理、日志记录和AjaxResult响应格式，遵循现有auth_views.py的代码风格和架构模式
- 微信认证视图GenericAPIView模式：视图继承GenericAPIView并配置serializer_class，创建对应的序列化器（WechatLoginSerializer用于参数验证，WechatRefreshSerializer保持API一致性），使用self.get_serializer()进行数据验证，包含完整的ValidationError异常处理和错误信息提取，遵循现有ChangePassword视图的架构模式
- 外部API响应透传模式：直接返回外部认证服务器的响应数据和状态码，使用AjaxResult构造器接收外部响应的code、msg、state、data字段，调用to_json_response()方法返回，包含JSON解析异常处理，避免根据状态码进行业务判断和转换，保持外部服务响应的原始性
- 微信认证URL路由配置模式：在apps/user/urls.py中导入wechat_auth_views模块，添加微信登录路由wechat/login/和微信刷新令牌路由wechat/refresh/，使用.as_view()方法注册视图类，保持与现有认证路由的命名和结构一致性，添加中文注释说明路由用途
- WxPermission权限类：专门用于微信小程序端API授权，仅允许用户名以"wx_"开头的已认证用户访问，在视图中使用permission_classes = [WxPermission]应用
- 腾讯云FaceID人脸核身视图开发模式：在apps/wechat/views/faceid_views.py中实现FaceIDAuthView继承GenericAPIView，接收id_card和name参数进行身份证格式验证和姓名验证，调用utils.tencent_faceid_utils.detect_auth()函数，根据腾讯云API响应的Response.Error或Response.Url字段返回AjaxResult.fail()或AjaxResult.success()，包含完整异常处理、日志记录和参数验证，序列化器重构为serializers包结构使用下划线命名规范
- 腾讯云FaceID人脸核身openid参数处理模式：在FaceIDAuthSerializer中添加openid字段（无长度限制，仅基本格式验证），在FaceIDAuthView中获取openid参数并进行脱敏日志记录，但不传递给detect_auth()函数，保持腾讯云API调用的原始参数（仅id_card和name），openid参数为后续功能扩展预留，添加中文注释说明参数用途和处理逻辑
- WechatBasicPermission权限类：专门用于微信小程序端基础API授权，仅允许用户名以"wx_"开头的已认证用户访问，在视图中使用permission_classes = [WechatBasicPermission]应用；WechatFaceAuthPermission权限类：专门用于微信小程序端高级API授权，实现三重验证机制（用户认证+微信用户名格式+人脸核身验证），包含同步人脸认证处理逻辑，在视图中使用permission_classes = [WechatFaceAuthPermission]应用
- Django项目SPECTACULAR_SETTINGS多级菜单优化最佳实践：使用后处理钩子postprocess_schema_tags基于URL路径自动生成"父级 > 子级"格式标签，动态获取Django应用AppConfig的verbose_name作为父级名称，通过RESOURCE_NAME_MAPPING配置子级名称，实现API文档的多级菜单结构展示
- AssetPackageManagementListSerializer新增mapped_field_names字段：SerializerMethodField类型，返回所有mapping对象中mapped_field_config.field_name值的Python列表，位置在field_mappings_detail字段之后，使用get_mapped_field_names方法实现，只提取有mapped_field_config配置的字段名称
