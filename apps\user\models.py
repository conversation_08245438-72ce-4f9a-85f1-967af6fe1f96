from django.db import models


class SystemUser(models.Model):
    id = models.IntegerField(verbose_name="用户id", primary_key=True)
    username = models.Char<PERSON>ield(max_length=255, verbose_name="唯一标识", unique=True)
    is_superuser = models.<PERSON>oleanField(null=True, verbose_name="")
    is_staff = models.BooleanField(null=True, verbose_name="")
    is_active = models.BooleanField(null=True, verbose_name="")
    email = models.CharField(max_length=50, null=True, blank=True, verbose_name="邮件地址")
    phone_number = models.CharField(max_length=50, null=True, blank=True, verbose_name="手机号码")
    id_card_number = models.CharField(max_length=50, null=True, blank=True, verbose_name="身份证号码")
    app_id = models.CharField(max_length=50, null=True, blank=True, verbose_name="应用编号")
    group_id = models.IntegerField(null=True, verbose_name="部门id")
    group_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="部门名称")
    role_id = models.IntegerField(null=True, verbose_name="角色id")
    role_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="角色名称")

    # 人脸核身相关字段
    real_name = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="真实姓名", help_text="用户的真实姓名，通过人脸核身验证获得"
    )
    biz_token = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="人脸核身业务标识",
        help_text="人脸核身流程的唯一业务标识，用于追踪核身流程",
    )
    detect_auth_time = models.DateTimeField(
        blank=True, null=True, verbose_name="人脸核身时间", help_text="人脸核身完成的日期时间"
    )
    detect_auth_result = models.BooleanField(
        default=False,
        null=True,
        blank=True,
        verbose_name="人脸核身结果",
        help_text="人脸核身结果：True表示核身成功，False表示核身失败",
    )

    # 微信小程序相关字段
    wechat_openid = models.CharField(  # 微信小程序用户唯一标识（只针对当前的小程序有效）
        max_length=64, unique=True, null=True, blank=True, verbose_name="微信OpenID", help_text="微信小程序用户唯一标识"
    )
    wechat_unionid = models.CharField(
        max_length=64, null=True, blank=True, verbose_name="微信UnionID", help_text="微信开放平台用户唯一标识"
    )
    wechat_session_key = models.CharField(
        max_length=128, null=True, blank=True, verbose_name="微信SessionKey", help_text="微信会话密钥(加密存储)"
    )
    wechat_nickname = models.CharField(max_length=64, null=True, blank=True, verbose_name="微信昵称")
    wechat_avatar_url = models.URLField(null=True, blank=True, verbose_name="微信头像URL")
    wechat_bind_time = models.DateTimeField(null=True, blank=True, verbose_name="微信绑定时间")

    class Meta:
        db_table = "t_sys_auth_user"
        verbose_name = "用户信息表"

    def __str__(self):
        return self.username


class SystemGroup(models.Model):
    id = models.IntegerField(verbose_name="部门id", primary_key=True)
    name = models.CharField(max_length=255, null=True, blank=True, verbose_name="部门名称")

    class Meta:
        db_table = "t_sys_auth_group"
        verbose_name = "用户部门表"

    def __str__(self):
        return self.name
