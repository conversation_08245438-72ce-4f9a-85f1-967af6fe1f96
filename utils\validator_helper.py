#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : validator_helper.py
<AUTHOR> JT_DA
@Date     : 2025/07/14
@File_Desc: 验证帮助工具
"""

import re
from id_validator import validator


def validate_phone_number(phone: str) -> bool:
    """
    验证中国大陆手机号码格式是否合法

    支持格式：
    - 11位数字，以1开头
    - 第二位为3、4、5、6、7、8、9
    - 后9位为任意数字

    Args:
        phone: 手机号码字符串

    Returns:
        bool: 验证是否通过
    """
    if not isinstance(phone, str):
        return False

    # 移除可能的空格、短横线等分隔符
    phone = re.sub(r'[\s\-]', '', phone)

    # 中国大陆手机号正则表达式
    # 1开头，第二位为3-9，总共11位数字
    pattern = r'^1[3-9]\d{9}$'

    return bool(re.match(pattern, phone))


def validate_id_card(id_card: str) -> bool:
    """
    验证中国大陆18位身份证号码格式是否合法

    使用 id-validator 库进行专业的身份证号码验证，包括：
    - 18位长度验证
    - 地区码验证（前6位）
    - 出生日期验证（第7-14位）
    - 校验码验证（最后一位）
    - 格式规范验证

    Args:
        id_card: 身份证号码字符串

    Returns:
        bool: 验证是否通过
    """
    if not isinstance(id_card, str):
        return False

    try:
        # 使用 id-validator 库进行验证
        return validator.is_valid(id_card)
    except Exception:
        # 如果验证过程中出现异常，返回False
        return False


def validate_social_credit_code(code: str) -> bool:
    """
    验证统一社会信用代码是否合法(GB 32100-2015标准)

    Args:
        code: 18位统一社会信用代码字符串

    Returns:
        bool: 验证是否通过
    """
    # 校验码字典(不使用I,O,Z,S,V)
    CODE_DICT = {
        "0": 0,
        "1": 1,
        "2": 2,
        "3": 3,
        "4": 4,
        "5": 5,
        "6": 6,
        "7": 7,
        "8": 8,
        "9": 9,
        "A": 10,
        "B": 11,
        "C": 12,
        "D": 13,
        "E": 14,
        "F": 15,
        "G": 16,
        "H": 17,
        "J": 18,
        "K": 19,
        "L": 20,
        "M": 21,
        "N": 22,
        "P": 23,
        "Q": 24,
        "R": 25,
        "T": 26,
        "U": 27,
        "W": 28,
        "X": 29,
        "Y": 30,
    }

    WEIGHTS = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28]

    if not isinstance(code, str) or len(code) != 18:
        return False

    try:
        weighted_sum = sum(CODE_DICT[char] * weight for char, weight in zip(code[:17], WEIGHTS))
        check_code = 31 - (weighted_sum % 31)
        check_code = 0 if check_code == 31 else check_code
        return check_code == CODE_DICT[code[-1]]
    except KeyError:
        return False 