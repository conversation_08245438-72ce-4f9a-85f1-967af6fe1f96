# Generated by Django 4.1.13 on 2025-07-22 12:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('counterparty', '0001_initial'),
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetPackageFieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('field_name', models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='字段名称')),
                ('field_type', models.CharField(blank=True, choices=[('text', '文本类型'), ('date', '日期类型'), ('numeric', '数值类型'), ('amount', '金额类型')], max_length=20, null=True, verbose_name='字段类型')),
                ('data_validation', models.CharField(blank=True, choices=[('none', '无校验'), ('phone', '手机号格式校验'), ('id_card', '身份证格式校验'), ('social_credit_code', '统一社会信用代码校验')], help_text='选择字段数据的校验规则：无校验、手机号格式校验、身份证格式校验、统一社会信用代码校验', max_length=20, null=True, verbose_name='数据校验')),
                ('is_masked', models.BooleanField(default=False, verbose_name='是否脱敏')),
                ('prefix_keep_chars', models.PositiveIntegerField(default=0, verbose_name='前保留字符数')),
                ('suffix_keep_chars', models.PositiveIntegerField(default=0, verbose_name='后保留字符数')),
                ('display_order', models.PositiveIntegerField(default=100, verbose_name='显示顺序')),
            ],
            options={
                'verbose_name': '资产包字段配置',
                'verbose_name_plural': '资产包字段配置',
                'db_table': 't_asset_package_field_config',
            },
        ),
        migrations.CreateModel(
            name='AssetPackageFieldMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('original_field_name', models.CharField(help_text='Excel文件中的原始表头字段名称', max_length=255, verbose_name='原表头字段名称')),
                ('mapped_field_config', models.ForeignKey(blank=True, help_text='映射到的标准字段配置，允许为空', null=True, on_delete=django.db.models.deletion.SET_NULL, to='data_governance.assetpackagefieldconfig', verbose_name='映射字段配置')),
            ],
            options={
                'verbose_name': '资产包字段映射',
                'verbose_name_plural': '资产包字段映射',
                'db_table': 't_asset_package_field_mapping',
            },
        ),
        migrations.CreateModel(
            name='AssetPackageManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('package_name', models.CharField(help_text='资产包的业务名称标识', max_length=255, verbose_name='资产包名称')),
                ('source_file', models.FileField(help_text='上传的资产包Excel文件', upload_to='./upload', verbose_name='源文件')),
                ('file_size', models.BigIntegerField(blank=True, help_text='文件大小(字节)', null=True, verbose_name='源文件大小')),
                ('upload_time', models.DateTimeField(blank=True, help_text='文件上传或重新上传的时间戳', null=True, verbose_name='上传时间')),
                ('package_status', models.CharField(choices=[('available', '可用'), ('unavailable', '不可用')], default='available', help_text='资产包的可用状态，基于数据验证结果自动设置', max_length=20, verbose_name='资产包状态')),
                ('unavailable_reason', models.TextField(blank=True, help_text='当资产包状态为不可用时，存储具体的数据验证失败信息', null=True, verbose_name='不可用原因')),
                ('creditor', models.ForeignKey(blank=True, help_text='关联的债权人信息', null=True, on_delete=django.db.models.deletion.SET_NULL, to='counterparty.creditorbasicinfo', verbose_name='债权人')),
                ('field_mappings', models.ManyToManyField(blank=True, help_text='该资产包的字段映射配置', to='data_governance.assetpackagefieldmapping', verbose_name='字段映射')),
                ('uploader', models.ForeignKey(blank=True, help_text='上传文件的用户', null=True, on_delete=django.db.models.deletion.SET_NULL, to='user.systemuser', verbose_name='上传人')),
            ],
            options={
                'verbose_name': '资产包管理',
                'verbose_name_plural': '资产包管理',
                'db_table': 't_asset_package_management',
            },
        ),
    ]
