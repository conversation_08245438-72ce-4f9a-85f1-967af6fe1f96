# Generated by Django 4.1.13 on 2025-07-26 15:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('data_governance', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='assetpackagemanagement',
            name='file_size',
            field=models.BigIntegerField(blank=True, help_text='文件大小(字节)', null=True, verbose_name='原文件大小'),
        ),
        migrations.AlterField(
            model_name='assetpackagemanagement',
            name='package_status',
            field=models.CharField(choices=[('available', '可用'), ('unavailable', '不可用')], default='unavailable', help_text='资产包的可用状态，基于数据验证结果自动设置', max_length=20, verbose_name='资产包状态'),
        ),
        migrations.AlterField(
            model_name='assetpackagemanagement',
            name='source_file',
            field=models.FileField(help_text='上传的资产包Excel文件', upload_to='./upload', verbose_name='原文件'),
        ),
    ]
