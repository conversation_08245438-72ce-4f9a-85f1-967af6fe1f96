# Generated by Django 4.1.13 on 2025-07-26 22:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('data_governance', '0003_assetpackagefieldmapping_mapped_debtor_field_config'),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetPackageManagementFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('file', models.FileField(help_text='资产包相关的附件文件', upload_to='./upload', verbose_name='附件文件')),
                ('file_name', models.CharField(help_text='上传文件的原始名称', max_length=255, verbose_name='附件原名')),
            ],
            options={
                'verbose_name': '资产包管理附件',
                'verbose_name_plural': '资产包管理附件',
                'db_table': 't_asset_package_management_file',
            },
        ),
        migrations.AddField(
            model_name='assetpackagemanagement',
            name='mediation_config',
            field=models.JSONField(blank=True, help_text='JSON格式的调解配置信息', null=True, verbose_name='调解信息配置'),
        ),
        migrations.AddField(
            model_name='assetpackagemanagement',
            name='attachments',
            field=models.ManyToManyField(blank=True, help_text='该资产包的相关附件文件', to='data_governance.assetpackagemanagementfile', verbose_name='附件文件'),
        ),
    ]
