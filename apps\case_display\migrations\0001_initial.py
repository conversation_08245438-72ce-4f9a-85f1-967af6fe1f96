# Generated by Django 4.1.13 on 2025-07-22 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CaseDisplay',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('case_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='案例名称')),
                ('case_date', models.DateField(blank=True, null=True, verbose_name='案例日期')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='涉及金额')),
                ('mediation_result', models.TextField(blank=True, null=True, verbose_name='调解结果')),
                ('case_details', models.TextField(blank=True, null=True, verbose_name='案例详情')),
            ],
            options={
                'verbose_name': '案例展示',
                'verbose_name_plural': '案例展示',
                'db_table': 't_case_display',
            },
        ),
    ]
