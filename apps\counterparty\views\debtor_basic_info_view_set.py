#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : debtor_basic_info_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/07/14
@File_Desc: 债务人基本信息视图集
"""

from django.db import transaction
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend

from apps.counterparty.models import DebtorBasicInfo
from apps.counterparty.serializers import (
    DebtorBasicInfoSerializer,
    DebtorBasicInfoCreateSerializer,
    DebtorBasicInfoUpdateSerializer,
)
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult


class BaseDebtorBasicInfoViewSet(viewsets.ModelViewSet):
    """
    债务人基本信息基础ViewSet

    提供债务人基本信息管理的基础功能，包括查询、过滤、搜索和分页等核心操作。
    支持按债务人类型和证件类型进行过滤，支持按债务人名称和证件号码进行搜索。

    注意：id_number（证件号码）字段具有唯一性约束，不允许重复值。
    """

    queryset = DebtorBasicInfo.objects.all().order_by("-created_time")
    serializer_class = DebtorBasicInfoSerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["debtor_type", "id_type"]  # 支持按债务人类型和证件类型过滤
    search_fields = ["debtor_name", "id_number"]  # 支持按债务人名称和证件号码搜索


class DebtorBasicInfoViewSet(BaseDebtorBasicInfoViewSet):
    """
    债务人基本信息ViewSet

    提供债务人基本信息的完整CRUD操作，支持嵌套创建和更新联系方式信息。
    包括电话、邮箱、地址、微信等多种联系方式的统一管理。

    注意：id_number（证件号码）字段具有唯一性约束，不允许重复值。
    """

    def get_serializer_class(self):
        """
        根据action选择相应的序列化器

        根据不同的操作类型选择对应的序列化器，确保数据验证和处理的准确性。
        创建操作使用CreateSerializer，更新操作使用UpdateSerializer，其他操作使用基础Serializer。
        """
        if self.action == "create":
            return DebtorBasicInfoCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return DebtorBasicInfoUpdateSerializer
        else:
            return DebtorBasicInfoSerializer

    def list(self, request, *args, **kwargs):
        """
        获取债务人基本信息列表

        提供分页查询债务人基本信息的功能，支持多种过滤和搜索条件，
        用于债务人信息的批量浏览和管理。

        **请求参数**
        - **查询参数**：
          - page (int, 可选): 页码，默认为1
          - page_size (int, 可选): 每页数量，默认为5，最大为9999
          - debtor_type (str, 可选): 债务人类型过滤，可选值：individual(自然人)、legal_person(法人)、other(其他)
          - id_type (str, 可选): 证件类型过滤，可选值：id_card(身份证)、passport(护照)、business_license(营业执照)
          - search (str, 可选): 搜索关键词，支持按债务人名称和证件号码搜索

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "success",
            "state": "success",
            "data": {
                "links": {
                    "next": "http://example.com/api/debtors/?page=2",
                    "previous": null
                },
                "count": 25,
                "results": [
                    {
                        "id": 1,
                        "debtor_type": "individual",
                        "debtor_type_cn": "自然人",
                        "debtor_name": "张三",
                        "id_type": "id_card",
                        "id_type_cn": "身份证",
                        "id_number": "110101199001011234",
                        "phones": [
                            {
                                "id": 1,
                                "phone_number": "***********",
                                "phone_type": "mobile",
                                "is_primary": true
                            }
                        ],
                        "emails": [
                            {
                                "id": 1,
                                "email_address": "<EMAIL>",
                                "email_type": "personal",
                                "is_primary": true
                            }
                        ],
                        "addresses": [
                            {
                                "id": 1,
                                "address_detail": "北京市朝阳区某某街道123号",
                                "address_type": "home",
                                "is_primary": true
                            }
                        ],
                        "wechats": [
                            {
                                "id": 1,
                                "wechat_id": "zhangsan_wx",
                                "wechat_nickname": "张三",
                                "is_primary": true
                            }
                        ]
                    }
                ]
            }
        }
        ```
        """
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        """
        创建债务人基本信息

        创建新的债务人基本信息记录，支持同时创建关联的联系方式信息，
        包括电话、邮箱、地址、微信等多种联系方式的嵌套创建。

        **请求参数**
        - **请求体参数**：
          - debtor_type (str, 必需): 债务人类型，可选值：individual(自然人)、legal_person(法人)、other(其他)
          - debtor_name (str, 必需): 债务人名称，长度不少于2个字符
          - id_type (str, 必需): 证件类型，可选值：id_card(身份证)、passport(护照)、business_license(营业执照)
          - id_number (str, 必需): 证件号码，长度不少于6位，需与证件类型匹配
          - phones (array, 可选): 电话信息数组
            - phone_number (str, 必需): 电话号码
            - phone_type (str, 可选): 电话类型，可选值：mobile(手机)、landline(座机)、fax(传真)
            - is_primary (bool, 可选): 是否为主要联系方式，默认false
          - emails (array, 可选): 邮箱信息数组
            - email_address (str, 必需): 邮箱地址
            - email_type (str, 可选): 邮箱类型，可选值：personal(个人)、work(工作)、other(其他)
            - is_primary (bool, 可选): 是否为主要联系方式，默认false
          - addresses (array, 可选): 地址信息数组
            - address_detail (str, 必需): 详细地址
            - address_type (str, 可选): 地址类型，可选值：home(家庭)、work(工作)、other(其他)
            - is_primary (bool, 可选): 是否为主要联系方式，默认false
          - wechats (array, 可选): 微信信息数组
            - wechat_id (str, 必需): 微信号
            - wechat_nickname (str, 可选): 微信昵称
            - is_primary (bool, 可选): 是否为主要联系方式，默认false

        **请求数据示例**
        ```json
        {
            "debtor_type": "individual",
            "debtor_name": "张三",
            "id_type": "id_card",
            "id_number": "110101199001011234",
            "phones": [
                {
                    "phone_number": "***********",
                    "phone_type": "mobile",
                    "is_primary": true
                }
            ],
            "emails": [
                {
                    "email_address": "<EMAIL>",
                    "email_type": "personal",
                    "is_primary": true
                }
            ],
            "addresses": [
                {
                    "address_detail": "北京市朝阳区某某街道123号",
                    "address_type": "home",
                    "is_primary": true
                }
            ],
            "wechats": [
                {
                    "wechat_id": "zhangsan_wx",
                    "wechat_nickname": "张三",
                    "is_primary": true
                }
            ]
        }
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "新增成功",
            "state": "success",
            "data": null
        }
        ```
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():  # 使用数据库事务确保数据一致性
            serializer.save()
        return AjaxResult.success(msg="新增成功")

    def retrieve(self, request, *args, **kwargs):
        """
        获取债务人基本信息详情

        根据债务人ID获取完整的债务人基本信息，包括所有关联的联系方式信息，
        用于债务人信息的详细查看和编辑前的数据加载。

        **请求参数**
        - **路径参数**：
          - id (int, 必需): 债务人ID，用于唯一标识债务人记录

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "success",
            "state": "success",
            "data": {
                "id": 1,
                "debtor_type": "individual",
                "debtor_type_cn": "自然人",
                "debtor_name": "张三",
                "id_type": "id_card",
                "id_type_cn": "身份证",
                "id_number": "110101199001011234",
                "phones": [
                    {
                        "id": 1,
                        "phone_number": "***********",
                        "phone_type": "mobile",
                        "is_primary": true
                    }
                ],
                "emails": [
                    {
                        "id": 1,
                        "email_address": "<EMAIL>",
                        "email_type": "personal",
                        "is_primary": true
                    }
                ],
                "addresses": [
                    {
                        "id": 1,
                        "address_detail": "北京市朝阳区某某街道123号",
                        "address_type": "home",
                        "is_primary": true
                    }
                ],
                "wechats": [
                    {
                        "id": 1,
                        "wechat_id": "zhangsan_wx",
                        "wechat_nickname": "张三",
                        "is_primary": true
                    }
                ]
            }
        }
        ```
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)

    def update(self, request, *args, **kwargs):
        """
        更新债务人基本信息

        完整更新债务人基本信息及其关联的联系方式，支持嵌套更新操作。
        仅支持完整更新（PUT方法），不支持部分更新（PATCH方法）。

        **请求参数**
        - **路径参数**：
          - id (int, 必需): 债务人ID，用于唯一标识要更新的债务人记录
        - **请求体参数**：
          - debtor_type (str, 必需): 债务人类型，可选值：individual(自然人)、legal_person(法人)、other(其他)
          - debtor_name (str, 必需): 债务人名称，长度不少于2个字符
          - id_type (str, 必需): 证件类型，可选值：id_card(身份证)、passport(护照)、business_license(营业执照)
          - id_number (str, 必需): 证件号码，长度不少于6位，需与证件类型匹配
          - phones (array, 可选): 电话信息数组，支持新增、修改、删除操作
          - emails (array, 可选): 邮箱信息数组，支持新增、修改、删除操作
          - addresses (array, 可选): 地址信息数组，支持新增、修改、删除操作
          - wechats (array, 可选): 微信信息数组，支持新增、修改、删除操作

        **请求数据示例**
        ```json
        {
            "debtor_type": "individual",
            "debtor_name": "张三",
            "id_type": "id_card",
            "id_number": "110101199001011234",
            "phones": [
                {
                    "id": 1,
                    "phone_number": "***********",
                    "phone_type": "mobile",
                    "is_primary": true
                }
            ],
            "emails": [
                {
                    "id": 1,
                    "email_address": "<EMAIL>",
                    "email_type": "personal",
                    "is_primary": true
                }
            ],
            "addresses": [
                {
                    "id": 1,
                    "address_detail": "北京市朝阳区某某街道123号",
                    "address_type": "home",
                    "is_primary": true
                }
            ],
            "wechats": [
                {
                    "id": 1,
                    "wechat_id": "zhangsan_wx",
                    "wechat_nickname": "张三",
                    "is_primary": true
                }
            ]
        }
        ```

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "编辑成功",
            "state": "success",
            "data": null
        }
        ```
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():  # 使用数据库事务确保数据一致性
            serializer.save()
        return AjaxResult.success(msg="编辑成功")

    def partial_update(self, request, *args, **kwargs):
        """
        禁用部分更新操作

        为了确保数据完整性和业务逻辑的一致性，本系统不支持PATCH方法的部分更新操作。
        所有更新操作必须使用PUT方法进行完整更新，确保所有必要的字段都被正确处理和验证。

        **请求参数**
        - **路径参数**：
          - id (int, 必需): 债务人ID

        **响应数据结构**
        ```json
        {
            "code": 405,
            "msg": "不支持部分更新操作，请使用PUT方法进行完整更新",
            "data": null
        }
        ```

        **替代方案**
        - 使用PUT方法进行完整更新
        - 在PUT请求中包含所有需要的字段数据
        - 先通过GET方法获取完整数据，修改后再通过PUT方法提交
        """
        from rest_framework.response import Response
        from rest_framework import status

        return Response(
            {
                "code": 405,
                "msg": "不支持部分更新操作，请使用PUT方法进行完整更新",
                "data": None
            },
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )

    def destroy(self, request, *args, **kwargs):
        """
        删除债务人基本信息

        删除指定的债务人基本信息记录及其所有关联的联系方式信息。
        删除操作不可逆，请谨慎操作。

        **请求参数**
        - **路径参数**：
          - id (int, 必需): 债务人ID，用于唯一标识要删除的债务人记录

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "删除成功",
            "state": "success",
            "data": null
        }
        ```
        """
        instance = self.get_object()
        with transaction.atomic():  # 使用数据库事务确保数据一致性
            instance.delete()
        return AjaxResult.success(msg="删除成功")

    @action(detail=False, methods=["get"])
    def choices(self, request):
        """
        获取债务人相关选择项

        获取债务人类型和证件类型的所有可选项，用于前端表单的下拉框选项填充。
        提供中文显示名称，便于用户理解和选择。

        **请求参数**
        - 无需任何参数

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "success",
            "state": "success",
            "data": {
                "debtor_types": {
                    "individual": "自然人",
                    "legal_person": "法人",
                    "other": "其他"
                },
                "id_types": {
                    "id_card": "身份证",
                    "passport": "护照",
                    "business_license": "营业执照"
                }
            }
        }
        ```
        """
        data = {
            "debtor_types": dict(DebtorBasicInfo.DEBTOR_TYPE_CHOICES),
            "id_types": dict(DebtorBasicInfo.ID_TYPE_CHOICES),
        }

        return AjaxResult.success(data=data)
