#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : apps.py
<AUTHOR> JT_DA
@Date     : 2025/07/29
@File_Desc: Django主应用配置，负责加载drf-spectacular扩展
"""

from django.apps import AppConfig


class OpsManagementConfig(AppConfig):
    """
    运营管理主应用配置类
    
    负责在Django应用启动时加载drf-spectacular的自定义扩展和钩子函数，
    确保多级菜单自动生成功能正常工作。
    """
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'ops_management'
    verbose_name = '智能处置运营管理'
    
    def ready(self):
        """
        Django应用就绪时的回调方法
        
        在这里导入schema扩展，确保drf-spectacular的自定义钩子函数
        在应用启动时被正确注册和加载。
        """
        try:
            # 导入schema扩展配置，确保自定义钩子函数被注册
            import ops_management.schema_extensions  # noqa: E402
        except ImportError:
            # 如果导入失败，记录警告但不影响应用启动
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("Failed to import schema_extensions. drf-spectacular customizations may not work.")
