#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : case_display_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/07/10
@File_Desc: 案例展示视图集
"""

from rest_framework import viewsets
from django.db import transaction
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend

from apps.case_display.models import CaseDisplay
from apps.case_display.serializers import CaseDisplaySerializer
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult
from utils.permission_helper import permission_required


class BaseCaseDisplayViewSet(viewsets.ModelViewSet):
    """
    案例展示基础视图集

    提供案例展示管理的基础功能配置，包括查询集、序列化器、分页和过滤配置。
    该基础类定义了案例展示的通用操作规范和数据访问模式，支持按案例日期过滤
    和按案例名称、调解结果、案例详情进行全文搜索。
    """
    queryset = CaseDisplay.objects.all().order_by("-case_date")
    serializer_class = CaseDisplaySerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["case_date"]  # 支持按案例日期过滤
    search_fields = ["case_name", "mediation_result", "case_details"]  # 支持按案例名称、调解结果、案例详情搜索


class CaseDisplayViewSet(BaseCaseDisplayViewSet):
    """
    案例展示视图集

    提供案例展示管理的完整业务功能，包括标准CRUD操作和案例数据的专业管理。
    该视图集是案例展示系统的核心组件，负责调解案例的展示、创建、更新和删除操作，
    支持按案例日期过滤和多字段搜索功能。
    """
    
    # @permission_required(per="智能处置运营管理.案例展示_查询")
    def list(self, request, *args, **kwargs):
        """
        获取案例展示列表

        获取系统中所有案例展示的分页列表，支持按案例日期过滤和多字段搜索功能。
        该接口为案例展示管理提供完整的数据展示和业务查询支持。

        **请求参数：**
        - case_date (日期, 可选): 案例日期，用于过滤指定日期的案例，格式：YYYY-MM-DD
        - search (字符串, 可选): 搜索关键词，支持在案例名称、调解结果、案例详情中进行模糊搜索
        - page (整数, 可选): 页码，从1开始，默认为1
        - page_size (整数, 可选): 每页记录数，默认20，最大100

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "count": 50,
                "next": "http://example.com/api/case_display/?page=3",
                "previous": "http://example.com/api/case_display/?page=1",
                "results": [
                    {
                        "id": 1,
                        "case_name": "某银行信用卡债务调解案例",
                        "case_date": "2025-01-15",
                        "amount": "50000.00",
                        "mediation_result": "双方达成分期还款协议，债务人同意在12个月内分期偿还",
                        "case_details": "债务人因经营困难导致信用卡逾期，经调解员协调，最终达成分期还款方案"
                    },
                    {
                        "id": 2,
                        "case_name": "个人借贷纠纷调解案例",
                        "case_date": "2025-01-10",
                        "amount": "80000.00",
                        "mediation_result": "调解成功，债务人一次性偿还本金，免除部分利息",
                        "case_details": "个人借贷纠纷，通过调解员专业协调，双方达成和解协议"
                    }
                ]
            }
        }
        ```
        """
        return super().list(request, *args, **kwargs)

    # @permission_required(per="智能处置运营管理.案例展示_新增")
    def create(self, request, *args, **kwargs):
        """
        创建新的案例展示

        创建一个新的案例展示记录，用于展示成功的调解案例和经验分享。
        该接口支持完整的案例信息录入，包括案例基本信息、涉及金额、调解结果和详细描述。

        **请求参数：**
        - case_name (字符串, 可选): 案例名称，用于标识案例，最大长度255字符
        - case_date (日期, 可选): 案例日期，记录案例发生的日期，格式：YYYY-MM-DD
        - amount (数字, 可选): 涉及金额，案例中的债务金额，最大15位数字，2位小数，不能为负数
        - mediation_result (字符串, 可选): 调解结果，描述案例的调解结果和达成的协议
        - case_details (字符串, 可选): 案例详情，详细描述案例的背景、过程和解决方案

        **请求数据示例：**
        ```json
        {
            "case_name": "某银行信用卡债务调解案例",
            "case_date": "2025-01-15",
            "amount": "50000.00",
            "mediation_result": "双方达成分期还款协议，债务人同意在12个月内分期偿还",
            "case_details": "债务人因经营困难导致信用卡逾期，经调解员协调，最终达成分期还款方案"
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "新增成功",
            "data": null
        }
        ```
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():  # 使用数据库事务确保数据一致性
            serializer.save()
        return AjaxResult.success(msg="新增成功")

    # @permission_required(per="智能处置运营管理.案例展示_查询")
    def retrieve(self, request, *args, **kwargs):
        """
        获取案例展示详细信息

        根据案例ID获取完整的案例展示详细信息，包括案例的所有字段数据。
        该接口用于案例详情页面展示和编辑操作的数据加载。

        **请求参数：**
        - id (整数, 必需): 案例展示的唯一标识ID，通过URL路径传递

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "id": 1,
                "case_name": "某银行信用卡债务调解案例",
                "case_date": "2025-01-15",
                "amount": "50000.00",
                "mediation_result": "双方达成分期还款协议，债务人同意在12个月内分期偿还",
                "case_details": "债务人因经营困难导致信用卡逾期，经调解员协调，最终达成分期还款方案"
            }
        }
        ```
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)

    # @permission_required(per="智能处置运营管理.案例展示_修改")
    def update(self, request, *args, **kwargs):
        """
        更新案例展示信息

        更新现有案例展示的完整信息，支持所有字段的修改。
        该接口用于案例信息的编辑和维护，确保案例数据的准确性和时效性。

        **请求参数：**
        - id (整数, 必需): 案例展示的唯一标识ID，通过URL路径传递
        - case_name (字符串, 可选): 案例名称，更新案例标识名称，最大长度255字符
        - case_date (日期, 可选): 案例日期，更新案例发生日期，格式：YYYY-MM-DD
        - amount (数字, 可选): 涉及金额，更新案例债务金额，最大15位数字，2位小数，不能为负数
        - mediation_result (字符串, 可选): 调解结果，更新案例的调解结果描述
        - case_details (字符串, 可选): 案例详情，更新案例的详细信息和背景描述

        **请求数据示例：**
        ```json
        {
            "case_name": "某银行信用卡债务调解案例（更新）",
            "case_date": "2025-01-20",
            "amount": "55000.00",
            "mediation_result": "双方达成分期还款协议，债务人同意在18个月内分期偿还",
            "case_details": "债务人因经营困难导致信用卡逾期，经调解员多次协调，最终达成更优化的分期还款方案"
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "编辑成功",
            "data": null
        }
        ```
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():  # 使用数据库事务确保数据一致性
            serializer.save()
        return AjaxResult.success(msg="编辑成功")

    def partial_update(self, request, *args, **kwargs):
        """
        禁用部分更新（PATCH）方法

        为了确保数据完整性和业务逻辑的一致性，本系统不支持部分更新操作。
        请使用PUT方法进行完整更新，确保所有必要的字段都被正确处理。

        **错误响应：**
        - 405: Method Not Allowed - 不支持PATCH方法

        **替代方案：**
        - 使用PUT方法进行完整更新
        - 在PUT请求中包含所有需要的字段数据
        """
        from rest_framework.response import Response
        from rest_framework import status

        return Response(
            {
                "code": 405,
                "msg": "不支持部分更新操作，请使用PUT方法进行完整更新",
                "data": None
            },
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )

    # @permission_required(per="智能处置运营管理.案例展示_删除")
    def destroy(self, request, *args, **kwargs):
        """
        删除案例展示

        根据案例ID删除指定的案例展示记录。该操作为物理删除，删除后数据无法恢复，
        请谨慎操作。建议在删除前确认案例不再需要或已做好数据备份。

        **请求参数：**
        - id (整数, 必需): 案例展示的唯一标识ID，通过URL路径传递

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "删除成功",
            "data": null
        }
        ```
        """
        instance = self.get_object()
        instance.delete()  # 执行物理删除操作
        return AjaxResult.success(msg="删除成功")
