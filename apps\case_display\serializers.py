#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : serializers.py
<AUTHOR> JT_DA
@Date     : 2025/07/10
@File_Desc: 案例展示序列化器
"""

from rest_framework import serializers
from apps.case_display.models import CaseDisplay


class CaseDisplaySerializer(serializers.ModelSerializer):
    """案例展示序列化器"""
    
    class Meta:
        model = CaseDisplay
        fields = ['id', 'case_name', 'case_date', 'amount', 'mediation_result', 'case_details']
        
    def validate_amount(self, value):
        """验证金额字段"""
        if value is not None and value < 0:
            raise serializers.ValidationError("涉及金额不能为负数")
        return value 