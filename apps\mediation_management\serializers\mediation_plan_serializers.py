#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_plan_serializers.py
<AUTHOR> JT_DA
@Date     : 2025/07/17
@File_Desc: 调解方案序列化器
"""

import json
from rest_framework import serializers
from django.db import transaction

from apps.mediation_management.models import MediationPlan
from apps.data_governance.models import AssetPackageManagement


class MediationPlanListSerializer(serializers.ModelSerializer):
    """调解方案列表序列化器"""

    # 方案状态中文显示
    plan_status_cn = serializers.CharField(source="get_plan_status_display", read_only=True)

    # 审批状态中文显示
    approval_status_cn = serializers.CharField(source="get_approval_status_display", read_only=True)

    # 关联资产包名称显示
    asset_package_name = serializers.CharField(source="asset_package.package_name", read_only=True)

    # 关联调解案件编号显示
    mediation_case_number = serializers.Char<PERSON>ield(source="mediation_case.case_number", read_only=True)

    # 关联调解案件状态显示
    mediation_case_status = serializers.CharField(source="mediation_case.case_status", read_only=True)

    # 映射字段名称列表 - 从关联的资产包获取字段映射配置
    mapped_field_names = serializers.SerializerMethodField()

    class Meta:
        model = MediationPlan
        fields = [
            "id",
            "plan_name",
            "plan_status_cn",
            "approval_status_cn",
            "approval_comment",
            "plan_config",
            "asset_package",
            "asset_package_name",
            "mediation_case",
            "mediation_case_number",
            "mediation_case_status",
            "mapped_field_names",
        ]

    def get_mapped_field_names(self, obj):
        """
        获取映射字段名称列表

        优先级逻辑：
        1. 优先使用 asset_package 字段直接获取资产包
        2. 仅当 asset_package 为 null 时，才通过 mediation_case.asset_package 获取

        Returns:
            list: 映射字段名称列表，只包含有 mapped_field_config 配置的字段名称
        """
        # 获取资产包对象，按优先级顺序
        asset_package = None

        # 方式一：直接通过 asset_package 字段获取
        if obj.asset_package:
            asset_package = obj.asset_package
        # 方式二：仅当 asset_package 为 null 时，通过 mediation_case 获取
        elif obj.mediation_case and obj.mediation_case.asset_package:
            asset_package = obj.mediation_case.asset_package

        # 如果没有找到资产包，返回空列表
        if not asset_package:
            return []

        # 获取资产包的字段映射关系
        mappings = asset_package.field_mappings.all()

        field_names = []
        for mapping in mappings:
            # 只提取有映射配置的字段名称
            if mapping.mapped_field_config:
                field_names.append(mapping.mapped_field_config.field_name)

        return field_names


class MediationPlanCreateSerializer(serializers.ModelSerializer):
    """调解方案创建序列化器"""

    # plan_config字段处理 - 接收JSON字符串或字典
    plan_config = serializers.JSONField(help_text="方案配置信息，JSON格式")

    class Meta:
        model = MediationPlan
        fields = ["plan_name", "plan_config", "asset_package", "mediation_case"]

    def validate_plan_name(self, value):
        """验证方案名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("方案名称不能为空")

        # 移除名称唯一性检查，允许方案名称重复
        return value.strip()

    def validate_plan_config(self, value):
        """验证方案配置"""
        if not value:
            raise serializers.ValidationError("方案配置不能为空")

        # 如果是字符串，尝试解析为JSON验证格式
        if isinstance(value, str):
            try:
                json.loads(value)
            except json.JSONDecodeError:
                raise serializers.ValidationError("方案配置必须是有效的JSON格式")

        return value

    def validate(self, data):
        """全局验证"""
        # 验证资产包和调解案件不能同时为空
        asset_package = data.get("asset_package")
        mediation_case = data.get("mediation_case")

        if not asset_package and not mediation_case:
            raise serializers.ValidationError("资产包和调解案件至少需要关联一个")

        return data

    def create(self, validated_data):
        """创建调解方案记录"""
        with transaction.atomic():  # 使用数据库事务确保数据一致性
            # 实现自动审批逻辑
            asset_package = validated_data.get("asset_package")

            if asset_package:
                # 如果关联了资产包，自动设置为已通过状态
                validated_data["approval_status"] = "approved"
                validated_data["plan_status"] = "active"
            else:
                # 否则设置为待审批状态
                validated_data["approval_status"] = "pending"
                validated_data["plan_status"] = "inactive"

            # 创建调解方案记录
            mediation_plan = MediationPlan.objects.create(**validated_data)
            return mediation_plan


class MediationPlanUpdateSerializer(serializers.ModelSerializer):
    """调解方案更新序列化器"""

    # plan_config字段处理 - 接收JSON字符串或字典
    plan_config = serializers.JSONField(required=False, help_text="方案配置信息，JSON格式")

    class Meta:
        model = MediationPlan
        fields = ["plan_name", "plan_config", "asset_package", "mediation_case"]

    def validate_plan_name(self, value):
        """验证方案名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("方案名称不能为空")

        # 移除名称唯一性检查，允许方案名称重复
        return value.strip()

    def validate_plan_config(self, value):
        """验证方案配置"""
        if value is None:
            return value

        # 如果是字符串，尝试解析为JSON验证格式
        if isinstance(value, str):
            try:
                json.loads(value)
            except json.JSONDecodeError:
                raise serializers.ValidationError("方案配置必须是有效的JSON格式")

        return value

    def validate(self, data):
        """全局验证"""
        # 获取当前实例的值，用于验证
        instance = self.instance

        # 获取要更新的值或当前值
        asset_package = data.get("asset_package", instance.asset_package)
        mediation_case = data.get("mediation_case", instance.mediation_case)

        # 验证资产包和调解案件不能同时为空
        if not asset_package and not mediation_case:
            raise serializers.ValidationError("资产包和调解案件至少需要关联一个")

        return data

    def update(self, instance, validated_data):
        """更新调解方案记录"""
        with transaction.atomic():  # 使用数据库事务确保数据一致性
            # 更新基本字段
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            instance.save()

            return instance


class MediationPlanApprovalSerializer(serializers.ModelSerializer):
    """调解方案审批序列化器"""

    # 审批状态字段 - 仅允许修改为已通过或未通过状态
    approval_status = serializers.ChoiceField(
        choices=[
            ('approved', '已通过'),
            ('rejected', '未通过'),
        ],
        help_text="审批状态：approved(已通过)、rejected(未通过)",
    )

    # 审批意见字段 - 可选的审批说明（现在是模型字段，无需重新定义）

    class Meta:
        model = MediationPlan
        fields = ["approval_status", "approval_comment"]

    def validate_approval_status(self, value):
        """验证审批状态"""
        # 获取当前实例的审批状态
        current_status = self.instance.approval_status if self.instance else None

        # 如果当前状态已经是已通过，不允许修改为未通过状态
        if current_status == "approved" and value == "rejected":
            raise serializers.ValidationError("已通过的方案不能修改为未通过状态")

        return value

    def update(self, instance, validated_data):
        """更新审批状态并实现状态联动"""
        with transaction.atomic():  # 使用数据库事务确保数据一致性
            approval_status = validated_data.get("approval_status")

            # 更新审批状态
            instance.approval_status = approval_status

            # 实现状态联动逻辑
            if approval_status == "approved":
                # 审批通过时，自动设置方案为已生效
                instance.plan_status = "active"
            else:
                # 其他状态时，自动设置方案为未生效
                instance.plan_status = "inactive"

            # 保存审批意见（现在是模型字段，直接保存）
            approval_comment = validated_data.get("approval_comment")
            if approval_comment is not None:
                instance.approval_comment = approval_comment

            instance.save()
            return instance
