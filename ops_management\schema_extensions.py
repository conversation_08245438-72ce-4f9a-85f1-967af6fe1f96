#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : schema_extensions.py
<AUTHOR> JT_DA
@Date     : 2025/07/29
@File_Desc: drf-spectacular schema扩展配置，用于自动加载自定义钩子和扩展
"""

# 导入自定义钩子函数，确保它们被注册到drf-spectacular中
from utils.spectacular_hooks import (
    postprocess_schema_tags
)

# 这个文件的主要目的是确保自定义钩子函数被正确导入和注册
# 当Django应用启动时，这些钩子函数将通过settings.py中的配置被自动调用

# 可以在这里添加更多的schema扩展配置
# 例如：自定义认证扩展、序列化器扩展等

# 注意：这个文件会被ops_management/apps.py在ready()方法中导入
# 确保所有扩展在Django应用启动时被正确加载
