# -*- coding: utf-8 -*-
import os
import hashlib
import hmac
import json
import sys
import time
import requests  # 使用requests库进行HTTP请求
from datetime import datetime


def sign(key, msg):
    return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()


# 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
# 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性
# 以下代码示例仅供参考，建议采用更安全的方式来使用密钥
# 请参见：https://cloud.tencent.com/document/product/1278/85305
# 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
secret_id = os.getenv("TENCENTCLOUD_SECRET_ID")
secret_key = os.getenv("TENCENTCLOUD_SECRET_KEY")

# 检查必需的环境变量是否已设置
if not secret_id:
    print("错误：未设置环境变量 TENCENTCLOUD_SECRET_ID")
    print("请设置环境变量：export TENCENTCLOUD_SECRET_ID=your_secret_id")
    sys.exit(1)

if not secret_key:
    print("错误：未设置环境变量 TENCENTCLOUD_SECRET_KEY")
    print("请设置环境变量：export TENCENTCLOUD_SECRET_KEY=your_secret_key")
    sys.exit(1)

token = ""

service = "faceid"
host = "faceid.tencentcloudapi.com"
region = ""
version = "2018-03-01"
action = "DetectAuth"
payload = '{"RuleId":"1", "IdCard": "210102196107295895", "Name": "沈真玉", "RedirectUrl": "https://www.qq.com"}'
params = json.loads(payload)
endpoint = "https://faceid.tencentcloudapi.com"
algorithm = "TC3-HMAC-SHA256"
timestamp = int(time.time())
date = datetime.utcfromtimestamp(timestamp).strftime("%Y-%m-%d")

# ************* 步骤 1：拼接规范请求串 *************
http_request_method = "POST"
canonical_uri = "/"
canonical_querystring = ""
ct = "application/json; charset=utf-8"
canonical_headers = "content-type:%s\nhost:%s\nx-tc-action:%s\n" % (ct, host, action.lower())
signed_headers = "content-type;host;x-tc-action"
hashed_request_payload = hashlib.sha256(payload.encode("utf-8")).hexdigest()
canonical_request = (
    http_request_method
    + "\n"
    + canonical_uri
    + "\n"
    + canonical_querystring
    + "\n"
    + canonical_headers
    + "\n"
    + signed_headers
    + "\n"
    + hashed_request_payload
)

# ************* 步骤 2：拼接待签名字符串 *************
credential_scope = date + "/" + service + "/" + "tc3_request"
hashed_canonical_request = hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()
string_to_sign = algorithm + "\n" + str(timestamp) + "\n" + credential_scope + "\n" + hashed_canonical_request

# ************* 步骤 3：计算签名 *************
secret_date = sign(("TC3" + secret_key).encode("utf-8"), date)
secret_service = sign(secret_date, service)
secret_signing = sign(secret_service, "tc3_request")
signature = hmac.new(secret_signing, string_to_sign.encode("utf-8"), hashlib.sha256).hexdigest()

# ************* 步骤 4：拼接 Authorization *************
authorization = (
    algorithm
    + " "
    + "Credential="
    + secret_id
    + "/"
    + credential_scope
    + ", "
    + "SignedHeaders="
    + signed_headers
    + ", "
    + "Signature="
    + signature
)

# ************* 步骤 5：构造并发起请求 *************
headers = {
    "Authorization": authorization,
    "Content-Type": "application/json; charset=utf-8",
    "Host": host,
    "X-TC-Action": action,
    "X-TC-Timestamp": str(timestamp),  # 修复：将timestamp转换为字符串类型
    "X-TC-Version": version,
}
if region:
    headers["X-TC-Region"] = region
if token:
    headers["X-TC-Token"] = token

try:
    # 使用requests库发送POST请求到腾讯云API
    # 构造完整的请求URL
    url = f"https://{host}/"
    
    # 发送POST请求，传入headers和JSON数据
    response = requests.post(
        url=url,
        headers=headers,
        data=payload.encode("utf-8"),  # 保持与原始实现一致的编码方式
        timeout=30  # 添加超时设置，提高请求稳定性
    )
    
    # 输出响应内容
    print(response.text)
    
except requests.exceptions.RequestException as err:
    # 捕获requests库相关的所有异常
    print(f"请求异常: {err}")
except Exception as err:
    # 捕获其他所有异常
    print(f"未知错误: {err}")
