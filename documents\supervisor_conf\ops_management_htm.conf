[group:ops_management_htm]
programs=gunicorn_ops_management_htm,celery_worker_ops_management_htm,celery_beat_ops_management_htm

[program:gunicorn_ops_management_htm]
command=bash -c "source /root/miniconda3/bin/activate env_py310_hg && gunicorn authsage.wsgi:application -c gunicorn_config.py"  # 指定要运行的程序的命令
directory=/mnt/d/pythonProject/ops_management_htm  # 指定程序的工作目录
autostart=false  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/mnt/d/pythonProject/ops_management_htm/logs/gunicorn.err.log  # 错误日志文件路径
stdout_logfile=/mnt/d/pythonProject/ops_management_htm/logs/gunicorn.out.log  # 输出日志文件路径
user=root  # 运行进程的用户
environment=DM_HOME=/workdir/dm8,LD_LIBRARY_PATH="$LD_LIBRARY_PATH:/workdir/dm8/bin:/workdir/dm8/drivers/dpi/"

[program:celery_worker_ops_management_htm]
command=bash -c "source /root/miniconda3/bin/activate env_py310_hg && celery -A authsage worker --loglevel=info"  # 指定要运行的程序的命令
directory=/mnt/d/pythonProject/ops_management_htm  # 指定程序的工作目录
autostart=false  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/mnt/d/pythonProject/ops_management_htm/logs/worker.err.log  # 错误日志文件路径
stdout_logfile=/mnt/d/pythonProject/ops_management_htm/logs/worker.out.log  # 输出日志文件路径
user=root  # 运行进程的用户
environment=DM_HOME=/workdir/dm8,LD_LIBRARY_PATH="$LD_LIBRARY_PATH:/workdir/dm8/bin:/workdir/dm8/drivers/dpi/"

[program:celery_beat_ops_management_htm]
command=bash -c "source /root/miniconda3/bin/activate env_py310_hg && celery -A authsage beat --loglevel=info"  # 指定要运行的程序的命令
directory=/mnt/d/pythonProject/ops_management_htm  # 指定程序的工作目录
autostart=false  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/mnt/d/pythonProject/ops_management_htm/logs/beat.err.log  # 错误日志文件路径
stdout_logfile=/mnt/d/pythonProject/ops_management_htm/logs/beat.out.log  # 输出日志文件路径
user=root  # 运行进程的用户
environment=DM_HOME=/workdir/dm8,LD_LIBRARY_PATH="$LD_LIBRARY_PATH:/workdir/dm8/bin:/workdir/dm8/drivers/dpi/"
