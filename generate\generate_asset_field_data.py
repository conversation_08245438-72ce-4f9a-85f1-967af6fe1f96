#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资产包字段配置数据生成脚本
为AssetPackageFieldConfig模型生成20条债务基本信息字段的测试数据
"""

import os
import sys
import django
from django.core.exceptions import ValidationError
from django.db import transaction

# 设置Django环境 - 修正路径为项目根目录
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ops_management.settings')
django.setup()

from apps.data_governance.models import AssetPackageFieldConfig


def generate_field_data():
    """生成20条债务基本信息字段数据"""

    with transaction.atomic():  # 使用事务确保数据一致性
        # 清理现有数据 - 确保每次运行都从干净状态开始
        existing_count = AssetPackageFieldConfig.objects.count()
        if existing_count > 0:
            AssetPackageFieldConfig.objects.all().delete()
            print(f"✓ 已清理现有数据: {existing_count} 条记录")
        else:
            print("✓ 数据表为空，无需清理")

    # 债务基本信息字段配置（字段名称与类型、校验、脱敏的精确映射）
    field_configs = [
        {
            "field_name": "借款人名称",
            "field_type": "text",
            "data_validation": "none",
            "is_masked": True,  # 姓名需要脱敏
            "prefix_keep_chars": 1,
            "suffix_keep_chars": 0,
            "display_order": 1
        },
        {
            "field_name": "借款人身份证号码",
            "field_type": "text",
            "data_validation": "id_card",  # 身份证格式校验
            "is_masked": True,  # 身份证需要脱敏
            "prefix_keep_chars": 3,
            "suffix_keep_chars": 4,
            "display_order": 2
        },
        {
            "field_name": "合同号",
            "field_type": "text",
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 3
        },
        {
            "field_name": "借据号",
            "field_type": "text",
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 4
        },
        {
            "field_name": "业务类型",
            "field_type": "text",
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 5
        },
        {
            "field_name": "授信种类",
            "field_type": "text",
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 6
        },
        {
            "field_name": "发放日期",
            "field_type": "date",  # 日期类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 7
        },
        {
            "field_name": "到期日期",
            "field_type": "date",  # 日期类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 8
        },
        {
            "field_name": "贷款原发放金额（折人民币）",
            "field_type": "amount",  # 金额类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 9
        },
        {
            "field_name": "本金余额（折人民币）",
            "field_type": "amount",  # 金额类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 10
        },
        {
            "field_name": "债权总额（折人民币）",
            "field_type": "amount",  # 金额类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 11
        },
        {
            "field_name": "贷款利率（年利率）",
            "field_type": "text",  # 利率用文本存储
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 12
        },
        {
            "field_name": "逾期天数（截至封包日）",
            "field_type": "numeric",  # 数值类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 13
        },
        {
            "field_name": "贷款五级分类",
            "field_type": "text",
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 14
        },
        {
            "field_name": "还款方式",
            "field_type": "text",
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 15
        },
        {
            "field_name": "首次进入不良时间",
            "field_type": "date",  # 日期类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 16
        },
        {
            "field_name": "逾期日期",
            "field_type": "date",  # 日期类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 17
        },
        {
            "field_name": "封包日",
            "field_type": "date",  # 日期类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 18
        },
        {
            "field_name": "欠息额（折人民币）",
            "field_type": "amount",  # 金额类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 19
        },
        {
            "field_name": "保证人个数（若没有则填0）",
            "field_type": "numeric",  # 数值类型
            "data_validation": "none",
            "is_masked": False,
            "prefix_keep_chars": 0,
            "suffix_keep_chars": 0,
            "display_order": 20
        }
    ]
    
    print("开始生成AssetPackageFieldConfig数据...")

    # 扩大事务范围，包含整个数据生成过程
    with transaction.atomic():
        try:
            # 准备批量创建的对象列表
            field_objects = []
            for config in field_configs:
                field_obj = AssetPackageFieldConfig(
                    field_name=config["field_name"],
                    field_type=config["field_type"],
                    data_validation=config["data_validation"],
                    is_masked=config["is_masked"],
                    prefix_keep_chars=config["prefix_keep_chars"],
                    suffix_keep_chars=config["suffix_keep_chars"],
                    display_order=config["display_order"]
                )
                field_objects.append(field_obj)
                print(f"✓ 准备创建字段: {config['field_name']} (类型: {config['field_type']}, 校验: {config['data_validation']}, 脱敏: {config['is_masked']}, 顺序: {config['display_order']})")

            # 批量创建所有字段配置对象
            created_objects = AssetPackageFieldConfig.objects.bulk_create(field_objects)
            created_count = len(created_objects)

            print(f"\n数据生成完成!")
            print(f"成功创建: {created_count} 条")
            print(f"总计处理: {created_count} 条")

        except ValidationError as e:
            print(f"✗ 批量创建失败，数据验证错误: {e}")
            raise
        except Exception as e:
            print(f"✗ 批量创建失败，发生错误: {e}")
            raise


if __name__ == "__main__":
    try:
        generate_field_data()
    except Exception as e:
        print(f"脚本执行失败: {e}")
        sys.exit(1) 