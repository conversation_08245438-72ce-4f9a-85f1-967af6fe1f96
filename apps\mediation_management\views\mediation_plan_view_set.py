#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_plan_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/07/17
@File_Desc: 调解方案视图集
"""

from django.db import transaction
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend
import django_filters

from apps.mediation_management.models import MediationPlan
from apps.mediation_management.serializers.mediation_plan_serializers import (
    MediationPlanListSerializer,
    MediationPlanCreateSerializer,
    MediationPlanUpdateSerializer,
    MediationPlanApprovalSerializer
)
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult


class MediationPlanFilter(django_filters.FilterSet):
    """
    调解方案自定义过滤器

    提供调解方案的高级过滤功能，支持按状态、关联对象以及资产包关联状态进行筛选。
    """
    
    # 资产包关联状态过滤 - 支持筛选是否关联了资产包
    asset_package__isnull = django_filters.BooleanFilter(
        field_name='asset_package',
        lookup_expr='isnull',
        help_text='筛选资产包关联状态：true（未关联资产包）、false（已关联资产包）'
    )

    class Meta:
        model = MediationPlan
        fields = ['plan_status', 'approval_status', 'asset_package', 'mediation_case', 'asset_package__isnull']


class BaseMediationPlanViewSet(viewsets.ModelViewSet):
    """
    调解方案基础ViewSet

    提供调解方案管理的基础功能配置，包括查询集、序列化器、分页和过滤配置。
    为调解方案相关的视图集提供统一的基础配置和通用功能。
    """
    queryset = MediationPlan.objects.all().order_by("-created_time")
    serializer_class = MediationPlanListSerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = MediationPlanFilter  # 使用自定义过滤器类
    search_fields = ["plan_name", "asset_package__package_name", "mediation_case__case_number"]


class MediationPlanViewSet(BaseMediationPlanViewSet):
    """
    调解方案管理视图集

    提供调解方案的完整生命周期管理功能，包括方案创建、查询、更新、删除等标准CRUD操作。
    支持方案审批、状态管理等专业的方案处理功能。
    """

    def get_serializer_class(self):
        """
        根据不同的操作动态选择合适的序列化器
        """
        if self.action == 'create':
            return MediationPlanCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return MediationPlanUpdateSerializer
        elif self.action == 'approve':
            return MediationPlanApprovalSerializer
        else:
            return MediationPlanListSerializer
    
    def list(self, request, *args, **kwargs):
        """
        获取调解方案列表数据，支持分页查询、多条件过滤和关键词搜索功能。

        **请求参数：**

        **查询参数：**
        - plan_status (字符串, 可选): 方案状态，可选值：inactive、active
        - approval_status (字符串, 可选): 审批状态，可选值：pending、approved、rejected
        - asset_package (整数, 可选): 资产包ID，用于过滤指定资产包的方案
        - mediation_case (整数, 可选): 调解案件ID，用于过滤指定案件的方案
        - asset_package__isnull (布尔值, 可选): 筛选资产包关联状态，可选值：true、false
        - search (字符串, 可选): 搜索关键词，按方案名称进行模糊搜索
        - page (整数, 可选): 页码，从1开始，默认为1
        - page_size (整数, 可选): 每页记录数，默认20，最大100

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "count": 50,
                "next": "http://example.com/api/mediation-plans/?page=3",
                "previous": "http://example.com/api/mediation-plans/?page=1",
                "results": [
                    {
                        "id": 1,
                        "plan_name": "标准调解方案A",
                        "plan_status_cn": "已生效",
                        "approval_status_cn": "已通过",
                        "approval_comment": "方案配置合理，审批通过",
                        "plan_config": {
                            "payment_plan": "分期还款",
                            "discount_rate": 0.8,
                            "installment_count": 12,
                            "grace_period": 30
                        },
                        "asset_package": 1,
                        "asset_package_name": "2025年第一批资产包",
                        "mediation_case": 1,
                        "mediation_case_number": "GZTJ202507170001",
                        "mediation_case_status": "in_progress"
                    }
                ]
            }
        }
        ```
        """
        return super().list(request, *args, **kwargs)
    
    def create(self, request, *args, **kwargs):
        """
        创建新的调解方案记录，支持关联资产包或调解案件。

        **请求参数：**
        **请求体参数：**
        - plan_name (字符串, 必需): 方案名称，调解方案的业务名称标识
        - plan_config (JSON对象, 必需): 方案配置，JSON格式的方案详细配置信息
        - asset_package (整数, 可选): 资产包ID，该调解方案关联的资产包
        - mediation_case (整数, 可选): 调解案件ID，该方案关联的调解案件

        **请求数据示例：**
        ```json
        {
            "plan_name": "标准还款方案",
            "plan_config": {
                "payment_plan": "分期还款",
                "discount_rate": 0.8,
                "installment_count": 12,
                "grace_period": 30,
                "description": "适用于标准债务纠纷的分期还款方案"
            },
            "asset_package": 1,
            "mediation_case": null
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "调解方案创建成功",
            "data": {
                "id": 1,
                "plan_name": "标准还款方案",
                "plan_status": "active"
            }
        }
        ```
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():  # 使用数据库事务确保数据一致性
            mediation_plan = serializer.save()

        return AjaxResult.success(
            msg="调解方案创建成功",
            data={
                'id': mediation_plan.id,
                'plan_name': mediation_plan.plan_name,
                'plan_status': mediation_plan.plan_status
            }
        )

    def retrieve(self, request, *args, **kwargs):
        """
        获取指定调解方案的详细信息，包含方案的完整配置和关联信息。

        **请求参数：**
        **路径参数：**
        - id (整数, 必需): 调解方案ID，要查询的方案唯一标识

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "id": 1,
                "plan_name": "标准调解方案A",
                "plan_status_cn": "已生效",
                "approval_status_cn": "已通过",
                "approval_comment": "方案配置合理，审批通过",
                "plan_config": {
                    "payment_plan": "分期还款",
                    "discount_rate": 0.8,
                    "installment_count": 12,
                    "grace_period": 30,
                    "description": "适用于标准债务纠纷的分期还款方案"
                },
                "asset_package": 1,
                "asset_package_name": "2025年第一批资产包",
                "mediation_case": 1,
                "mediation_case_number": "GZTJ202507170001",
                "mediation_case_status": "in_progress"
            }
        }
        ```
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)

    def update(self, request, *args, **kwargs):
        """
        更新指定调解方案的信息，支持修改方案的基本信息、配置和关联关系。

        **请求参数：**
        **路径参数：**
        - id (整数, 必需): 调解方案ID，要更新的方案唯一标识
        **请求体参数：**
        - plan_name (字符串, 可选): 方案名称，调解方案的业务名称标识
        - plan_config (JSON对象, 可选): 方案配置，JSON格式的方案详细配置信息
        - asset_package (整数, 可选): 资产包ID，该调解方案关联的资产包
        - mediation_case (整数, 可选): 调解案件ID，该方案关联的调解案件

        **请求数据示例：**
        ```json
        {
            "plan_name": "标准还款方案（修订版）",
            "plan_config": {
                "payment_plan": "分期还款",
                "discount_rate": 0.75,
                "installment_count": 18,
                "grace_period": 45,
                "description": "修订后的标准还款方案，调整了折扣率和分期数"
            },
            "asset_package": 1,
            "mediation_case": 2
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "调解方案更新成功",
            "data": {
                "id": 1,
                "plan_name": "标准还款方案（修订版）",
                "plan_status": "inactive",
                "approval_status": "pending"
            }
        }
        ```
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():  # 使用数据库事务确保数据一致性
            updated_instance = serializer.save()

        return AjaxResult.success(
            msg="调解方案更新成功",
            data={
                'id': updated_instance.id,
                'plan_name': updated_instance.plan_name,
                'plan_status': updated_instance.plan_status,
                'approval_status': updated_instance.approval_status
            }
        )

    def partial_update(self, request, *args, **kwargs):
        """
        禁用部分更新（PATCH）方法，强制使用PUT方法进行完整更新。

        **请求参数：**
        - 不接受任何参数，该方法已被禁用

        **响应数据结构：**
        ```json
        {
            "code": 405,
            "msg": "不支持部分更新操作，请使用PUT方法进行完整更新",
            "data": null
        }
        ```
        """
        from rest_framework.response import Response
        from rest_framework import status

        return Response(
            {
                "code": 405,
                "msg": "不支持部分更新操作，请使用PUT方法进行完整更新",
                "data": None
            },
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )

    def destroy(self, request, *args, **kwargs):
        """
        删除指定的调解方案记录，包括相关的配置和关联数据。

        **请求参数：**
        **路径参数：**
        - id (整数, 必需): 调解方案ID，要删除的方案唯一标识

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "调解方案 '标准还款方案' 删除成功",
            "data": null
        }
        ```
        """
        instance = self.get_object()
        plan_name = instance.plan_name

        with transaction.atomic():  # 使用数据库事务确保数据一致性
            instance.delete()

        return AjaxResult.success(msg=f"调解方案 '{plan_name}' 删除成功")

    @action(detail=False, methods=['get'])
    def status_choices(self, request):
        """
        获取调解方案的状态选项数据，用于前端表单和筛选组件的选项展示。

        **请求参数：**
        - 无需任何参数

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "plan_status_choices": [
                    {"value": "inactive", "label": "未生效"},
                    {"value": "active", "label": "已生效"}
                ],
                "approval_status_choices": [
                    {"value": "pending", "label": "待审批"},
                    {"value": "approved", "label": "已通过"},
                    {"value": "rejected", "label": "未通过"}
                ]
            }
        }
        ```
        """
        try:
            # 获取方案状态选择项
            plan_status_choices = MediationPlan.PLAN_STATUS_CHOICES
            approval_status_choices = MediationPlan.APPROVAL_STATUS_CHOICES

            # 转换为前端需要的格式：[{"value": "field_name", "label": "display_name"}]
            plan_status_data = [
                {"value": choice[0], "label": choice[1]}
                for choice in plan_status_choices
            ]
            approval_status_data = [
                {"value": choice[0], "label": choice[1]}
                for choice in approval_status_choices
            ]

            data = {
                'plan_status_choices': plan_status_data,
                'approval_status_choices': approval_status_data,
            }

            return AjaxResult.success(
                msg="操作成功",
                data=data
            )

        except Exception as e:
            return AjaxResult.fail(msg=f"获取状态选择项失败: {str(e)}")

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """
        执行调解方案的审批操作，支持通过或拒绝两种状态。

        **请求参数：**
        **路径参数：**
        - id (整数, 必需): 调解方案ID，要审批的方案唯一标识
        **请求体参数：**
        - approval_status (字符串, 必需): 审批状态，可选值：approved、rejected
        - approval_comment (字符串, 可选): 审批意见，审批过程中的意见、说明或备注信息

        **请求数据示例：**
        ```json
        {
            "approval_status": "approved",
            "approval_comment": "方案配置合理，符合业务要求，审批通过"
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "调解方案审批成功",
            "data": {
                "id": 1,
                "plan_name": "标准还款方案",
                "approval_status": "approved",
                "plan_status": "active",
                "approval_comment": "方案配置合理，符合业务要求，审批通过"
            }
        }
        ```
        """
        mediation_plan = self.get_object()
        serializer = self.get_serializer(mediation_plan, data=request.data)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():  # 使用数据库事务确保数据一致性
            updated_plan = serializer.save()

        return AjaxResult.success(
            msg="调解方案审批成功",
            data={
                'id': updated_plan.id,
                'plan_name': updated_plan.plan_name,
                'approval_status': updated_plan.approval_status,
                'plan_status': updated_plan.plan_status,
                'approval_comment': updated_plan.approval_comment or ''
            }
        )
