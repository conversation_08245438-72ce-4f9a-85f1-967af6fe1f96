# -*- coding: utf-8 -*-
"""
调解管理模型定义
包含调解方案、调解案件和案件附件相关的数据模型
"""
import uuid
from django.db import models
from django.conf import settings
from django.utils import timezone
import hashlib
import secrets
import time
from ops_management.base_model import BaseModel
from apps.counterparty.models import DebtorBasicInfo, CreditorBasicInfo
from apps.data_governance.models import AssetPackageManagement
from apps.user.models import SystemUser


class MediationCaseFile(BaseModel):
    """调解案件附件模型 - 管理案件相关的附件文件"""

    # 安全标识符 - 用于文件下载的UUID标识符，防止路径暴露
    secure_token = models.UUIDField(
        default=uuid.uuid4,
        unique=True,
        verbose_name="安全标识符",
        help_text="用于文件下载的UUID标识符，确保文件路径不被暴露"
    )

    # 附件文件 - 实际存储的文件
    file = models.FileField(upload_to=settings.UPLOAD_DIR, verbose_name="附件文件", help_text="案件相关的附件文件")

    # 附件原名 - 上传文件的原始名称
    file_name = models.CharField(max_length=255, verbose_name="附件原名", help_text="上传文件的原始名称")

    class Meta:
        db_table = "t_mediation_case_file"
        verbose_name = "调解案件附件"
        verbose_name_plural = "调解案件附件"

    def __str__(self):
        return self.file_name or f"附件-{self.id}"


class MediationCase(BaseModel):
    """调解案件模型 - 管理调解案件的完整生命周期"""

    # 案件状态选择项
    CASE_STATUS_CHOICES = [
        ("draft", "待发起"),
        ("initiated", "已发起"),
        ("pending_confirm", "待确认"),
        ("in_progress", "进行中"),
        ("completed", "已完成"),
        ("closed", "已关闭"),
    ]

    # 协议公证状态选择项
    NOTARIZATION_STATUS_CHOICES = [
        ("not_notarized", "未公证"),
        ("notarized", "已公证"),
    ]

    # 调解案件号 - 唯一标识，格式：GZTJ+YYYYMMDD+6位安全哈希
    case_number = models.CharField(
        max_length=50, unique=True, verbose_name="调解案件号", help_text="格式：GZTJ+YYYYMMDD+6位安全哈希"
    )

    # 案件状态 - 案件的流程状态
    case_status = models.CharField(
        max_length=20,
        choices=CASE_STATUS_CHOICES,
        default="draft",
        verbose_name="案件状态",
        help_text="案件的流程处理状态",
    )

    # 债务人 - 案件的债务方当事人
    debtor = models.ForeignKey(
        DebtorBasicInfo,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="债务人",
        help_text="案件的债务方当事人",
    )

    # 债权人 - 案件的债权方当事人
    creditor = models.ForeignKey(
        CreditorBasicInfo,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="债权人",
        help_text="案件的债权方当事人",
    )

    # 调解员 - 负责案件调解的工作人员
    mediator = models.ForeignKey(
        SystemUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="调解员",
        help_text="负责案件调解的工作人员",
    )

    # 注意：mediation_config 字段已移除，调解配置信息现在通过关联的资产包获取

    # 确认的调解方案 - 案件采用的调解方案
    mediation_plan = models.ForeignKey(
        "MediationPlan",  # 使用字符串引用避免循环导入
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="确认的调解方案",
        help_text="案件采用的调解方案",
    )

    # 关联资产包 - 案件关联的资产包
    asset_package = models.ForeignKey(
        AssetPackageManagement,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="资产包",
        help_text="案件关联的资产包信息",
    )

    # 资产包应用行号 - 在资产包中对应的数据行号
    asset_package_row_number = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="资产包应用行号",
        help_text="案件在资产包Excel文件中对应的数据行号",
    )

    # 发起日期 - 案件正式发起的时间
    initiate_date = models.DateTimeField(null=True, blank=True, verbose_name="发起日期", help_text="案件正式发起的时间")

    # 关闭日期 - 案件结束关闭的时间
    close_date = models.DateTimeField(null=True, blank=True, verbose_name="关闭日期", help_text="案件结束关闭的时间")

    # 调解协议PDF - 调解达成的协议文件
    mediation_agreement = models.FileField(
        upload_to=settings.UPLOAD_DIR, null=True, blank=True, verbose_name="调解协议", help_text="调解达成的协议PDF文件"
    )

    # 电子签名图片 - 当事人的电子签名
    electronic_signature = models.ImageField(
        upload_to=settings.UPLOAD_DIR, null=True, blank=True, verbose_name="电子签名", help_text="当事人的电子签名图片"
    )

    # 协议公证状态 - 调解协议的公证状态
    notarization_status = models.CharField(
        max_length=20,
        choices=NOTARIZATION_STATUS_CHOICES,
        default="not_notarized",
        verbose_name="协议公证状态",
        help_text="调解协议的公证处理状态",
    )

    # 案件附件 - 与调解案件附件的多对多关系
    attachments = models.ManyToManyField(
        MediationCaseFile,
        blank=True,
        verbose_name="案件附件",
        help_text="案件相关的附件文件"
    )

    class Meta:
        db_table = "t_mediation_case"
        verbose_name = "调解案件"
        verbose_name_plural = "调解案件"

    def __str__(self):
        return self.case_number or f"调解案件-{self.id}"

    def save(self, *args, **kwargs):
        """
        重写save方法，自动生成安全的案件号

        安全特性：
        - 无法通过案件号推导其他案件号（基于SHA256哈希）
        - 无法从案件号获取案件数量信息（非连续序号）
        - 抗枚举攻击，6位哈希有1600万种组合
        - 向后兼容，不影响现有数据格式
        - 碰撞概率极低（约1/1600万）
        """
        if not self.case_number:
            # 生成安全案件号：GZTJ + YYYYMMDD + 6位哈希值
            today = timezone.now().strftime("%Y%m%d")
            prefix = f"GZTJ{today}"

            # 生成唯一的安全后缀，最多重试10次避免极低概率的碰撞
            max_retries = 10
            for attempt in range(max_retries):
                # 使用高精度时间戳和随机字节生成哈希源，确保不可预测性
                hash_source = f"{time.time_ns()}{secrets.token_hex(8)}{attempt}".encode('utf-8')
                # 生成SHA256哈希并取前6位转大写，提高可读性
                hash_suffix = hashlib.sha256(hash_source).hexdigest()[:6].upper()
                candidate_number = f"{prefix}{hash_suffix}"

                # 检查是否已存在（极低概率的碰撞检测）
                if not MediationCase.objects.filter(case_number=candidate_number).exists():
                    self.case_number = candidate_number
                    break
            else:
                # 如果10次重试都失败，使用时间戳作为后备方案确保系统可用性
                fallback_suffix = str(int(time.time_ns()))[-6:]
                self.case_number = f"{prefix}{fallback_suffix}"

        super().save(*args, **kwargs)


class MediationPlan(BaseModel):
    """调解方案模型 - 管理调解方案的配置和审批状态"""

    # 审批状态选择项
    APPROVAL_STATUS_CHOICES = [
        ("pending", "待审批"),
        ("approved", "已通过"),
        ("rejected", "未通过"),
    ]

    # 方案状态选择项
    PLAN_STATUS_CHOICES = [
        ("inactive", "未生效"),
        ("active", "已生效"),
    ]

    # 方案名称 - 调解方案的业务标识
    plan_name = models.CharField(max_length=200, verbose_name="方案名称", help_text="调解方案的业务名称标识")

    # 方案状态 - 方案的执行状态
    plan_status = models.CharField(
        max_length=20,
        choices=PLAN_STATUS_CHOICES,
        default="inactive",
        verbose_name="方案状态",
        help_text="方案的执行生效状态",
    )

    # 审批状态 - 方案的审批流程状态
    approval_status = models.CharField(
        max_length=20,
        choices=APPROVAL_STATUS_CHOICES,
        default="pending",
        verbose_name="审批状态",
        help_text="方案的审批流程状态",
    )

    # 方案配置 - JSON格式存储方案的详细配置信息
    plan_config = models.JSONField(verbose_name="方案配置", help_text="JSON格式的方案配置信息")

    # 审批意见 - 审批过程中的意见或说明
    approval_comment = models.TextField(
        null=True, blank=True, verbose_name="审批意见", help_text="审批过程中的意见、说明或备注信息"
    )

    # 关联资产包 - 该方案适用的资产包
    asset_package = models.ForeignKey(
        AssetPackageManagement,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="资产包",
        help_text="该调解方案关联的资产包",
    )

    # 关联调解案件 - 该方案关联的调解案件
    mediation_case = models.ForeignKey(
        MediationCase,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="调解案件",
        help_text="该方案关联的调解案件",
    )

    class Meta:
        db_table = "t_mediation_plan"
        verbose_name = "调解方案"
        verbose_name_plural = "调解方案"

    def __str__(self):
        return self.plan_name or f"调解方案-{self.id}"
