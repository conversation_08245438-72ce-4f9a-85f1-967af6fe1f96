[supervisord]
nodaemon=true

[program:gunicorn]
command=bash -c "gunicorn ops_management.wsgi:application -c gunicorn_config.py"  # 指定要运行的程序的命令
directory=/app  # 指定程序的工作目录
autostart=true  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/app/logs/gunicorn.err.log  # 错误日志文件路径
stdout_logfile=/app/logs/gunicorn.out.log  # 输出日志文件路径
user=root  # 运行进程的用户

[program:celery_worker]
command=bash -c "celery -A ops_management worker --loglevel=info"  # 指定要运行的程序的命令
directory=/app  # 指定程序的工作目录
autostart=true  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/app/logs/worker.err.log  # 错误日志文件路径
stdout_logfile=/app/logs/worker.out.log  # 输出日志文件路径
user=root  # 运行进程的用户名

[program:celery_beat]
command=bash -c "celery -A ops_management beat --loglevel=info"  # 指定要运行的程序的命令
directory=/app  # 指定程序的工作目录
autostart=true  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/app/logs/beat.err.log  # 错误日志文件路径
stdout_logfile=/app/logs/beat.out.log  # 输出日志文件路径
user=root  # 运行进程的用户名