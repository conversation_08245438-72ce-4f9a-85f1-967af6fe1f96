#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : expression_calculator.py
<AUTHOR> JT_DA
@Date     : 2025/07/30
@File_Desc: 表达式计算工具模块 - 提供通用的表达式计算功能
"""

import os
import re
import logging
import pandas as pd

# 获取日志记录器
logger = logging.getLogger(__name__)


def calculate_expression_with_asset_data(asset_package, row_number, expression, logic_type):
    """
    基于资产包数据计算表达式值
    
    该函数从指定的资产包和行号中提取数据，根据表达式和逻辑类型进行计算。
    支持文本格式化和数学运算两种处理模式。
    
    Args:
        asset_package: AssetPackageManagement对象，包含源文件和字段映射信息
        row_number (int): 数据行号，从1开始计数
        expression (str): 计算表达式，使用花括号{}包围变量名
        logic_type (str): 逻辑处理类型，可选值：
            - "text_format": 文本格式化模式，仅进行变量替换
            - "result_calculation": 结果运算模式，进行变量替换后使用数学计算
    
    Returns:
        dict: 包含计算结果的字典，格式：
            {
                "success": True/False,
                "result": "计算结果字符串" 或 None,
                "error": "错误信息" 或 None
            }
    
    Raises:
        无直接异常抛出，所有异常都被捕获并在返回值中体现
    """
    try:
        # 优化：检查表达式是否包含花括号变量
        # 如果表达式中不包含花括号变量，直接返回原始表达式，跳过后续的数据处理
        if not re.search(r'\{[^}]+\}', expression):
            logger.info(f"表达式不包含花括号变量，直接返回原始表达式: {expression}")
            return {"success": True, "result": expression, "error": None}

        # 检查资产包是否有源文件
        if not asset_package.source_file:
            logger.error(f"资产包 {asset_package.id} 没有源文件")
            return {"success": False, "result": None, "error": "资产包没有源文件"}
        
        # 获取字段映射配置
        field_mappings = asset_package.field_mappings.filter(
            mapped_field_config__isnull=False
        ).select_related('mapped_field_config')
        
        if not field_mappings.exists():
            logger.error(f"资产包 {asset_package.id} 没有配置字段映射")
            return {"success": False, "result": None, "error": "资产包没有配置字段映射"}
        
        # 构建字段名映射字典：{field_name: original_field_name}
        field_name_mapping = {}
        for mapping in field_mappings:
            field_name = mapping.mapped_field_config.field_name
            original_field_name = mapping.original_field_name
            field_name_mapping[field_name] = original_field_name
        
        # 读取Excel文件数据
        try:
            file_path = asset_package.source_file.path
            if not os.path.exists(file_path):
                logger.error(f"资产包文件不存在: {file_path}")
                return {"success": False, "result": None, "error": "资产包文件不存在"}
            
            # 使用pandas读取Excel文件
            df = pd.read_excel(file_path)
            
            if df.empty:
                logger.error(f"资产包文件为空: {file_path}")
                return {"success": False, "result": None, "error": "资产包文件为空"}
            
            # 检查行号是否有效（pandas索引从0开始，用户行号从1开始）
            data_row_index = row_number - 1
            if data_row_index >= len(df):
                logger.error(f"指定的行号 {row_number} 超出数据范围，文件共 {len(df)} 行")
                return {"success": False, "result": None, "error": f"指定的行号超出数据范围，文件共 {len(df)} 行"}
            
            # 获取指定行的数据
            row_data = df.iloc[data_row_index]
            
        except Exception as e:
            logger.error(f"读取资产包文件失败: {str(e)}")
            return {"success": False, "result": None, "error": "读取资产包文件失败"}
        
        # 解析表达式并替换变量
        try:
            # 提取表达式中的所有变量（花括号内的内容）
            variables = re.findall(r'\{([^}]+)\}', expression)
            
            if not variables:
                logger.error("表达式中没有找到有效的变量")
                return {"success": False, "result": None, "error": "表达式中没有找到有效的变量"}
            
            # 替换表达式中的变量为实际值
            calculation_expression = expression
            for variable in variables:
                # 检查变量是否在字段映射中
                if variable not in field_name_mapping:
                    logger.error(f"变量 '{variable}' 在字段映射中不存在")
                    return {"success": False, "result": None, "error": f"变量 '{variable}' 在字段映射中不存在"}
                
                # 获取原始字段名
                original_field_name = field_name_mapping[variable]
                
                # 检查原始字段是否在数据中存在
                if original_field_name not in row_data:
                    logger.error(f"原始字段 '{original_field_name}' 在数据中不存在")
                    return {"success": False, "result": None, "error": f"原始字段 '{original_field_name}' 在数据中不存在"}
                
                # 获取字段值
                field_value = row_data[original_field_name]

                # 根据逻辑类型处理字段值
                if logic_type == "result_calculation":
                    # 结果运算：处理空值和数值转换
                    if pd.isna(field_value):
                        field_value = 0
                    else:
                        try:
                            field_value = float(field_value)
                        except (ValueError, TypeError):
                            logger.error(f"字段 '{variable}' 的值 '{field_value}' 无法转换为数值")
                            return {"success": False, "result": None, "error": f"字段 '{variable}' 的值无法转换为数值"}
                else:
                    # 文本格式化：处理空值和字符串转换
                    if pd.isna(field_value):
                        field_value = ""
                    else:
                        field_value = str(field_value)

                # 替换表达式中的变量
                calculation_expression = calculation_expression.replace(f'{{{variable}}}', str(field_value))
            
            # 根据逻辑类型处理结果
            try:
                if logic_type == "result_calculation":
                    # 结果运算：使用eval计算表达式
                    result = eval(calculation_expression)

                    # 格式化数值结果
                    if isinstance(result, (int, float)):
                        formatted_result = f"{result:.2f}"
                    else:
                        formatted_result = str(result)
                else:
                    # 文本格式化：直接返回格式化后的字符串
                    formatted_result = calculation_expression

                logger.info(f"表达式处理成功，逻辑类型: {logic_type}, 结果: {formatted_result}")
                return {"success": True, "result": formatted_result, "error": None}

            except Exception as e:
                logger.error(f"表达式处理失败: {str(e)}")
                return {"success": False, "result": None, "error": "表达式有误"}
            
        except Exception as e:
            logger.error(f"表达式解析失败: {str(e)}")
            return {"success": False, "result": None, "error": "表达式解析失败"}
    
    except Exception as e:
        logger.error(f"计算表达式时发生异常: {str(e)}")
        return {"success": False, "result": None, "error": "计算过程中发生异常"}
