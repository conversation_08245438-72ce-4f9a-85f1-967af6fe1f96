#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : debtor_basic_info_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/07/14
@File_Desc: 债务人基本信息序列化器
"""

from rest_framework import serializers
from apps.counterparty.models import DebtorBasicInfo, DebtorPhone, DebtorEmail, DebtorAddress, DebtorWechat
from utils.validator_helper import validate_social_credit_code
from id_validator import validator


class DebtorPhoneSerializer(serializers.ModelSerializer):
    """债务人联系电话序列化器"""

    class Meta:
        model = DebtorPhone
        exclude = ["revision", "created_by", "created_time", "updated_by", "updated_time"]


class DebtorEmailSerializer(serializers.ModelSerializer):
    """债务人邮箱序列化器"""

    class Meta:
        model = DebtorEmail
        exclude = ["revision", "created_by", "created_time", "updated_by", "updated_time"]


class DebtorAddressSerializer(serializers.ModelSerializer):
    """债务人地址序列化器"""

    class Meta:
        model = DebtorAddress
        exclude = ["revision", "created_by", "created_time", "updated_by", "updated_time"]


class DebtorWechatSerializer(serializers.ModelSerializer):
    """债务人微信序列化器"""

    class Meta:
        model = DebtorWechat
        exclude = ["revision", "created_by", "created_time", "updated_by", "updated_time"]


class DebtorBasicInfoSerializer(serializers.ModelSerializer):
    """债务人基本信息序列化器

    注意：id_number（证件号码）字段具有唯一性约束，不允许重复值。
    """

    debtor_type_cn = serializers.CharField(source="get_debtor_type_display", read_only=True)
    id_type_cn = serializers.CharField(source="get_id_type_display", read_only=True)

    # 嵌套序列化器显示关联信息
    phones = DebtorPhoneSerializer(many=True, read_only=True)
    emails = DebtorEmailSerializer(many=True, read_only=True)
    addresses = DebtorAddressSerializer(many=True, read_only=True)
    wechats = DebtorWechatSerializer(many=True, read_only=True)

    class Meta:
        model = DebtorBasicInfo
        exclude = ["revision", "created_by", "created_time", "updated_by", "updated_time"]
        read_only_fields = [
            "id",
            "debtor_type_cn",
            "id_type_cn",
            "phones",
            "emails",
            "addresses",
            "wechats",
        ]

    def validate_id_number(self, value):
        """验证证件号码格式"""
        import re

        if not value:
            return value

        # 简单的证件号码格式验证
        if len(value) < 6:
            raise serializers.ValidationError("证件号码长度不能少于6位")

        return value

    def validate_debtor_name(self, value):
        """验证债务人名称"""
        if value and len(value.strip()) < 2:
            raise serializers.ValidationError("债务人名称长度不能少于2个字符")
        return value.strip() if value else value


class DebtorBasicInfoCreateSerializer(serializers.ModelSerializer):
    """创建债务人基本信息序列化器

    注意：id_number（证件号码）字段具有唯一性约束，不允许重复值。
    """

    # 支持ManyToManyField字段的嵌套创建
    phones = DebtorPhoneSerializer(many=True, required=False)
    emails = DebtorEmailSerializer(many=True, required=False)
    addresses = DebtorAddressSerializer(many=True, required=False)
    wechats = DebtorWechatSerializer(many=True, required=False)

    class Meta:
        model = DebtorBasicInfo
        fields = ["debtor_type", "debtor_name", "id_type", "id_number", "phones", "emails", "addresses", "wechats"]

    def validate_id_number(self, value):
        """验证证件号码格式"""
        import re

        if not value:
            return value

        # 简单的证件号码格式验证
        if len(value) < 6:
            raise serializers.ValidationError("证件号码长度不能少于6位")

        return value

    def validate_debtor_name(self, value):
        """验证债务人名称"""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("债务人名称不能为空且长度不能少于2个字符")
        return value.strip()

    def validate(self, data):
        """验证证件类型与证件号码的匹配"""
        id_type = data.get("id_type")
        id_number = data.get("id_number")

        if id_type and id_number:
            if id_type == "id_card":
                # 使用id-validator库验证身份证号码
                if not validator.is_valid(id_number):
                    raise serializers.ValidationError("身份证号码格式不正确")
            elif id_type == "business_license":
                # 使用自定义函数验证营业执照统一社会信用代码
                if not validate_social_credit_code(id_number):
                    raise serializers.ValidationError("营业执照统一社会信用代码格式不正确")

        return data

    def create(self, validated_data):
        """创建债务人信息及其关联数据"""
        # 提取ManyToManyField数据
        phones_data = validated_data.pop("phones", [])
        emails_data = validated_data.pop("emails", [])
        addresses_data = validated_data.pop("addresses", [])
        wechats_data = validated_data.pop("wechats", [])

        # 创建债务人基本信息
        debtor = DebtorBasicInfo.objects.create(**validated_data)

        # 创建并关联电话信息
        for phone_data in phones_data:
            phone = DebtorPhone.objects.create(**phone_data)
            debtor.phones.add(phone)

        # 创建并关联邮箱信息
        for email_data in emails_data:
            email = DebtorEmail.objects.create(**email_data)
            debtor.emails.add(email)

        # 创建并关联地址信息
        for address_data in addresses_data:
            address = DebtorAddress.objects.create(**address_data)
            debtor.addresses.add(address)

        # 创建并关联微信信息
        for wechat_data in wechats_data:
            wechat = DebtorWechat.objects.create(**wechat_data)
            debtor.wechats.add(wechat)

        return debtor


class DebtorBasicInfoUpdateSerializer(serializers.ModelSerializer):
    """更新债务人基本信息序列化器

    注意：id_number（证件号码）字段具有唯一性约束，不允许重复值。
    """

    # 支持ManyToManyField字段的嵌套更新
    phones = DebtorPhoneSerializer(many=True, required=False)
    emails = DebtorEmailSerializer(many=True, required=False)
    addresses = DebtorAddressSerializer(many=True, required=False)
    wechats = DebtorWechatSerializer(many=True, required=False)

    class Meta:
        model = DebtorBasicInfo
        fields = ["debtor_type", "debtor_name", "id_type", "id_number", "phones", "emails", "addresses", "wechats"]

    def validate_id_number(self, value):
        """验证证件号码格式"""
        if not value:
            return value

        # 简单的证件号码格式验证
        if len(value) < 6:
            raise serializers.ValidationError("证件号码长度不能少于6位")

        return value

    def validate_debtor_name(self, value):
        """验证债务人名称"""
        if value and len(value.strip()) < 2:
            raise serializers.ValidationError("债务人名称长度不能少于2个字符")
        return value.strip() if value else value

    def validate(self, data):
        """验证证件类型与证件号码的匹配"""
        id_type = data.get("id_type")
        id_number = data.get("id_number")

        if id_type and id_number:
            if id_type == "id_card":
                # 使用id-validator库验证身份证号码
                if not validator.is_valid(id_number):
                    raise serializers.ValidationError("身份证号码格式不正确")
            elif id_type == "business_license":
                # 使用自定义函数验证营业执照统一社会信用代码
                if not validate_social_credit_code(id_number):
                    raise serializers.ValidationError("营业执照统一社会信用代码格式不正确")

        return data

    def update(self, instance, validated_data):
        """更新债务人信息及其关联数据"""
        # 提取ManyToManyField数据
        phones_data = validated_data.pop("phones", None)
        emails_data = validated_data.pop("emails", None)
        addresses_data = validated_data.pop("addresses", None)
        wechats_data = validated_data.pop("wechats", None)

        # 更新基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 更新电话信息
        if phones_data is not None:
            instance.phones.clear()
            for phone_data in phones_data:
                phone = DebtorPhone.objects.create(**phone_data)
                instance.phones.add(phone)

        # 更新邮箱信息
        if emails_data is not None:
            instance.emails.clear()
            for email_data in emails_data:
                email = DebtorEmail.objects.create(**email_data)
                instance.emails.add(email)

        # 更新地址信息
        if addresses_data is not None:
            instance.addresses.clear()
            for address_data in addresses_data:
                address = DebtorAddress.objects.create(**address_data)
                instance.addresses.add(address)

        # 更新微信信息
        if wechats_data is not None:
            instance.wechats.clear()
            for wechat_data in wechats_data:
                wechat = DebtorWechat.objects.create(**wechat_data)
                instance.wechats.add(wechat)

        return instance
