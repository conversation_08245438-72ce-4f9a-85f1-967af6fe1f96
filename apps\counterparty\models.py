from django.db import models
from ops_management.base_model import BaseModel


# 迁移语句
# python manage.py makemigrations
# python manage.py migrate
class CreditorPhone(BaseModel):
    """债权人联系电话模型"""

    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="联系电话")
    phone_type = models.CharField(max_length=20, blank=True, null=True, verbose_name="电话类型")
    is_primary = models.BooleanField(default=False, verbose_name="是否主要联系方式")

    class Meta:
        db_table = "t_creditor_phone"
        verbose_name = "债权人联系电话"
        verbose_name_plural = "债权人联系电话"

    def __str__(self):
        return self.phone or str(self.id)


class CreditorEmail(BaseModel):
    """债权人邮箱模型"""

    email = models.EmailField(blank=True, null=True, verbose_name="邮箱地址")
    email_type = models.CharField(max_length=20, blank=True, null=True, verbose_name="邮箱类型")
    is_primary = models.BooleanField(default=False, verbose_name="是否主要联系方式")

    class Meta:
        db_table = "t_creditor_email"
        verbose_name = "债权人邮箱"
        verbose_name_plural = "债权人邮箱"

    def __str__(self):
        return self.email or str(self.id)


class CreditorAddress(BaseModel):
    """债权人地址模型"""

    address = models.TextField(blank=True, null=True, verbose_name="地址")
    address_type = models.CharField(max_length=20, blank=True, null=True, verbose_name="地址类型")
    is_primary = models.BooleanField(default=False, verbose_name="是否主要联系方式")

    class Meta:
        db_table = "t_creditor_address"
        verbose_name = "债权人地址"
        verbose_name_plural = "债权人地址"

    def __str__(self):
        return self.address or str(self.id)


class CreditorBasicInfo(BaseModel):
    """债权人基本信息模型"""

    # 债权人类型选择项
    CREDITOR_TYPE_CHOICES = [
        ("bank", "银行"),
        ("financial_asset_management", "金融资产管理公司"),
        ("consumer_finance", "消费金融公司"),
        ("auto_finance", "汽车金融公司"),
        ("financial_leasing", "金融租赁公司"),
        ("small_loan", "小额贷款公司"),
        ("financing_guarantee", "融资担保公司"),
        ("financing_leasing", "融资租赁公司"),
        ("local_asset_management", "地方资产管理公司"),
        ("lending_institution", "助贷机构"),
        ("insurance", "保险公司"),
        ("technology", "科技公司"),
        ("individual", "自然人"),
        ("other", "其他"),
    ]

    # 证件类型选择项
    ID_TYPE_CHOICES = [
        ("id_card", "身份证"),
        ("passport", "护照"),
        ("business_license", "营业执照"),
    ]

    creditor_type = models.CharField(
        max_length=50, choices=CREDITOR_TYPE_CHOICES, blank=True, null=True, verbose_name="债权人类型"
    )
    creditor_name = models.CharField(max_length=200, blank=True, null=True, verbose_name="债权人名称")
    id_type = models.CharField(max_length=20, choices=ID_TYPE_CHOICES, blank=True, null=True, verbose_name="证件类型")
    id_number = models.CharField(max_length=50, blank=True, null=True, verbose_name="证件号码")
    phones = models.ManyToManyField(CreditorPhone, blank=True, verbose_name="联系方式-电话")
    emails = models.ManyToManyField(CreditorEmail, blank=True, verbose_name="联系方式-邮箱")
    addresses = models.ManyToManyField(CreditorAddress, blank=True, verbose_name="联系方式-地址")

    class Meta:
        db_table = "t_creditor_basic_info"
        verbose_name = "债权人基本信息"
        verbose_name_plural = "债权人基本信息"

    def __str__(self):
        return self.creditor_name or str(self.id)


class DebtorPhone(BaseModel):
    """债务人联系电话模型"""

    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="联系电话")
    phone_type = models.CharField(max_length=20, blank=True, null=True, verbose_name="电话类型")
    is_primary = models.BooleanField(default=False, verbose_name="是否主要联系方式")

    class Meta:
        db_table = "t_debtor_phone"
        verbose_name = "债务人联系电话"
        verbose_name_plural = "债务人联系电话"

    def __str__(self):
        return self.phone or str(self.id)


class DebtorEmail(BaseModel):
    """债务人邮箱模型"""

    email = models.EmailField(blank=True, null=True, verbose_name="邮箱地址")
    email_type = models.CharField(max_length=20, blank=True, null=True, verbose_name="邮箱类型")
    is_primary = models.BooleanField(default=False, verbose_name="是否主要联系方式")

    class Meta:
        db_table = "t_debtor_email"
        verbose_name = "债务人邮箱"
        verbose_name_plural = "债务人邮箱"

    def __str__(self):
        return self.email or str(self.id)


class DebtorAddress(BaseModel):
    """债务人地址模型"""

    address = models.TextField(blank=True, null=True, verbose_name="地址")
    address_type = models.CharField(max_length=20, blank=True, null=True, verbose_name="地址类型")
    is_primary = models.BooleanField(default=False, verbose_name="是否主要联系方式")

    class Meta:
        db_table = "t_debtor_address"
        verbose_name = "债务人地址"
        verbose_name_plural = "债务人地址"

    def __str__(self):
        return self.address or str(self.id)


class DebtorWechat(BaseModel):
    """债务人微信模型"""

    wechat = models.CharField(max_length=50, blank=True, null=True, verbose_name="微信号")
    wechat_type = models.CharField(max_length=20, blank=True, null=True, verbose_name="微信类型")
    is_primary = models.BooleanField(default=False, verbose_name="是否主要联系方式")

    class Meta:
        db_table = "t_debtor_wechat"
        verbose_name = "债务人微信"
        verbose_name_plural = "债务人微信"

    def __str__(self):
        return self.wechat or str(self.id)


class DebtorBasicInfo(BaseModel):
    """债务人基本信息模型"""

    # 债务人类型选择项
    DEBTOR_TYPE_CHOICES = [
        ("individual", "自然人"),
        ("legal_person", "法人"),
        ("other", "其他"),
    ]

    # 证件类型选择项
    ID_TYPE_CHOICES = [
        ("id_card", "身份证"),
        ("passport", "护照"),
        ("business_license", "营业执照"),
    ]

    debtor_type = models.CharField(
        max_length=20, choices=DEBTOR_TYPE_CHOICES, blank=True, null=True, verbose_name="债务人类型"
    )
    debtor_name = models.CharField(max_length=200, blank=True, null=True, verbose_name="债务人名称")
    id_type = models.CharField(max_length=20, choices=ID_TYPE_CHOICES, blank=True, null=True, verbose_name="证件类型")
    id_number = models.CharField(max_length=50, blank=True, null=True, unique=True, verbose_name="证件号码")
    phones = models.ManyToManyField(DebtorPhone, blank=True, verbose_name="联系方式-电话")
    emails = models.ManyToManyField(DebtorEmail, blank=True, verbose_name="联系方式-邮箱")
    addresses = models.ManyToManyField(DebtorAddress, blank=True, verbose_name="联系方式-地址")
    wechats = models.ManyToManyField(DebtorWechat, blank=True, verbose_name="联系方式-微信")

    class Meta:
        db_table = "t_debtor_basic_info"
        verbose_name = "债务人基本信息"
        verbose_name_plural = "债务人基本信息"

    def __str__(self):
        return self.debtor_name or str(self.id)
