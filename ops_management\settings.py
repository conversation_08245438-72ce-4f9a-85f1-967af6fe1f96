"""
Django settings for ops_management project.

Generated by 'django-admin startproject' using Django 4.1.13.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

import os
from pathlib import Path

from gunicorn_config import env

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

config = os.environ.__dict__
config.update(env)

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-t@8=lrqh%h-@x4vh-h53ezi-=tb1d1ihg9069g)u8tt)=#-=&v"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config.get("debug", True)

ALLOWED_HOSTS = ["*"]


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_celery_results",
    "celery",
    "oauth2_provider",
    "rest_framework",
    "django_filters",
    "apps.user.apps.UserConfig",
    "apps.case_display.apps.CaseDisplayConfig",
    "apps.feedback.apps.FeedbackConfig",
    "apps.data_governance.apps.DataGovernanceConfig",
    "apps.counterparty.apps.CounterpartyConfig",
    "apps.outbound_communication.apps.OutboundCommunicationConfig",
    "apps.mediation_management.apps.MediationManagementConfig",
    "apps.wechat.apps.WechatConfig",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "oauth2_provider.middleware.OAuth2TokenMiddleware",  # OAuth2 token middleware
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "ops_management.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "ops_management.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": config.get("db_engine"),
        "NAME": config.get("db_name"),
        "USER": config.get("db_user"),
        "PASSWORD": config.get("db_password"),
        "HOST": config.get("db_host"),
        "PORT": config.get("db_port"),
        "OPTIONS": {
            "ssl_mode": "REQUIRED",
        },
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = "zh-Hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_ROOT = os.path.join(BASE_DIR, "static")
STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

UPLOAD_DIR = config.get("upload_dir", BASE_DIR / "upload")
LOG_DIR = config.get("log_dir", BASE_DIR / "logs")
BACKUP_DIR = config.get("backup_dir", BASE_DIR / "backups")

for d in [UPLOAD_DIR, LOG_DIR, BACKUP_DIR]:
    os.makedirs(d, exist_ok=True)

# 日志配置
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "[%(levelname)s] %(asctime)s %(module)s.%(funcName)s [%(process)d:%(thread)d] - %(filename)s[line:%(lineno)d] %(message)s",
        },
        "standard": {
            "format": "[%(levelname)s] %(asctime)s %(module)s - %(message)s",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "DEBUG",
            "formatter": "standard",
            "stream": "ext://sys.stdout",
        },
        "null": {
            "level": "DEBUG",
            "class": "logging.NullHandler",
        },
        "file_handler": {
            "class": "concurrent_log_handler.ConcurrentRotatingFileHandler",
            "level": "DEBUG",
            "formatter": "verbose",
            "encoding": "utf8",
            "filename": os.path.join(LOG_DIR, "syslog.log"),
            "maxBytes": 1 * 1024 * 1024,  # 1 MB
            "backupCount": 5,
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": True,
        },
        "django.db.backends": {
            "handlers": ["null"],
            "level": "INFO",
            "propagate": False,
        },
        "django.request": {
            "handlers": ["console", "file_handler"],
            "level": "ERROR",
            "propagate": False,
        },
    },
    "root": {"level": config.get("log_level", "INFO"), "handlers": ["file_handler"]},
}

# Celery配置
# 设置任务接受的类型，默认是{'json'}
CELERY_ACCEPT_CONTENT = ["application/json"]
# 设置task任务序列列化为json
CELERY_TASK_SERIALIZER = "json"
# 请任务接受后存储时的类型
CELERY_RESULT_SERIALIZER = "json"
# 时间格式化为中国时间
CELERY_TIMEZONE = "Asia/Shanghai"
# 是否使用UTC时间
CELERY_ENABLE_UTC = False
# 指定broker为redis 如果指定rabbitmq CELERY_BROKER_URL = 'amqp://guest:guest@localhost:5672//'
CELERY_BROKER_URL = config.get("celery_broker_url", "redis://localhost:6379/0")
# 指定存储结果的地方，支持使用rpc、数据库、redis等等，具体可参考文档 # CELERY_RESULT_BACKEND = 'db+mysql://scott:tiger@localhost/foo' # mysql 作为后端数据库
# CELERY_RESULT_BACKEND = 'redis://127.0.0.1:6379/4'
# 使用django数据库存储结果 来自 django_celery_results
CELERY_RESULT_BACKEND = "django-db"
# 结果的缓存配置 来自 django_celery_results
# CELERY_CACHE_BACKEND = 'django-cache'
# 设置任务过期时间 默认是一天，为None或0 表示永不过期
CELERY_TASK_RESULT_EXPIRES = 60 * 60 * 24
# 设置异步任务结果永不过期，如果不设置的话，每天04点celery会自动清空过期的异步任务结果
# CELERY_RESULT_EXPIRES = 0
# 设置worker并发数，默认是cpu核心数
CELERYD_CONCURRENCY = config.get("celeryd_concurrency", 4)
# 设置每个worker最大任务数
CELERYD_MAX_TASKS_PER_CHILD = 100
# 使用队列分流每个任务
# CELERY_QUEUES = (
#     Queue("add", Exchange("add"), routing_key="task_add"),
#     Queue("mul", Exchange("mul"), routing_key="task_mul"),
#     Queue("xsum", Exchange("xsum"), routing_key="task_xsum"),
# )
# 配置队列分流路由，注意可能无效，需要在运行异步任务时来指定不同的队列
# CELERY_ROUTES = {
#     'public.tasks.add': {'queue': 'add', 'routing_key':'task_add'},
#     'public.tasks.mul': {'queue': 'add', 'routing_key':'task_add'},
#     'public.tasks.xsum': {'queue': 'add', 'routing_key':'task_add'},
#     # 'public.tasks.mul': {'queue': 'mul', 'routing_key':'task_mul'},
#     # 'public.tasks.xsum': {'queue': 'xsum', 'routing_key':'task_xsum'},
#     }

# django rest framework config
REST_FRAMEWORK = {
    # 默认登录认证
    "DEFAULT_AUTHENTICATION_CLASSES": ("oauth2_provider.contrib.rest_framework.OAuth2Authentication",),
    # 默认权限认证 - 使用自定义权限类，包含用户名前缀拦截逻辑
    "DEFAULT_PERMISSION_CLASSES": ("utils.permission_helper.MyPermission",),
    # 默认异常处理器
    "EXCEPTION_HANDLER": "utils.exception_helper.custom_exception_handler",
    # 默认参数过滤器
    "DEFAULT_FILTER_BACKENDS": ("django_filters.rest_framework.DjangoFilterBackend",),
    # OAuth2 token验证失败时的处理
    "UNAUTHENTICATED_USER": None,
    "UNAUTHENTICATED_TOKEN": None,
    # 默认限流类配置 - 使用Django REST Framework内置限流类
    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.AnonRateThrottle",  # 匿名用户限流
        "rest_framework.throttling.UserRateThrottle",  # 认证用户限流
    ],
    # 默认限流速率配置 - 根据项目实际业务需求设置合理频率
    "DEFAULT_THROTTLE_RATES": {
        "anon": "100/hour",  # 匿名用户每小时100次请求
        "user": "1000/hour",  # 认证用户每小时1000次请求
    },
}


AUTH_SERVER_URL = config.get("auth_server_url", "")
AUTH_SERVER_FRONTEND_URL = config.get("auth_server_frontend_url", "")

OAUTH2_PROVIDER = {
    # 资源服务器内省配置
    "RESOURCE_SERVER_INTROSPECTION_URL": f"{AUTH_SERVER_URL}/o/introspect/",
    "RESOURCE_SERVER_INTROSPECTION_CREDENTIALS": (
        config.get("client_id", ""),
        config.get("client_secret", ""),
    ),
    # Token验证超时设置（秒）
    "RESOURCE_SERVER_TOKEN_CACHING_SECONDS": 3600,
    # 启用Bearer Token验证
    "RESOURCE_SERVER_BEARER_TOKEN_VERIFICATION": True,
    # EXP时间时区设置（重要：2.4.0版本修复）
    "AUTHENTICATION_SERVER_EXP_TIME_ZONE": "UTC",
    # 错误响应包含scope信息
    "ERROR_RESPONSE_WITH_SCOPES": True,
    # 资源服务器模式（不创建token，仅验证）
    "RESOURCE_SERVER_ONLY": True,
}


if DEBUG:
    INSTALLED_APPS += [
        "drf_spectacular",
    ]

    REST_FRAMEWORK["DEFAULT_SCHEMA_CLASS"] = "drf_spectacular.openapi.AutoSchema"
    REST_FRAMEWORK["COMPONENT_SPLIT_REQUEST"] = True

    SPECTACULAR_SETTINGS = {
        "TITLE": "华泰民智能处置运营管理API文档",
        "DESCRIPTION": "基于Django REST Framework的智能处置运营管理系统API文档，支持多级菜单结构展示",
        "VERSION": "1.0.0",
        "SERVE_INCLUDE_SCHEMA": True,  # 包含请求参数的模式
        # 'COMPONENT_SPLIT_REQUEST': True,
        # {{ Source: context7-mcp on 'drf-spectacular URL path processing configuration' }}
        # URL路径处理配置 - 用于多级菜单生成
        "SCHEMA_PATH_PREFIX": r"/",  # 设置路径前缀模式
        "SCHEMA_PATH_PREFIX_TRIM": False,  # 不移除路径前缀
        # 后处理钩子 - 优化标签结构和枚举处理
        "POSTPROCESSING_HOOKS": [
            "drf_spectacular.hooks.postprocess_schema_enums",  # 保留默认枚举处理
            "utils.spectacular_hooks.postprocess_schema_tags",  # 自定义标签后处理
        ],
        # 操作排序配置 - 确保API接口按逻辑顺序排列
        "SORT_OPERATIONS": True,
        "SWAGGER_UI_SETTINGS": {
            "deepLinking": True,
            "persistAuthorization": True,
            "displayOperationId": True,
            # {{ Source: context7-mcp on 'Swagger UI docExpansion configuration' }}
            # 控制API端点和模型的默认展开状态 - 优化多级菜单显示
            # "docExpansion": "list",  # 展开标签但折叠操作，便于查看多级菜单结构
            "docExpansion": "none",
            "defaultModelsExpandDepth": -1,  # 完全隐藏模型定义部分
            "defaultModelExpandDepth": 1,  # 单个模型示例的展开深度
            # 标签排序配置
            "tagsSorter": "alpha",  # 按字母顺序排序标签，确保层级结构清晰
            "operationsSorter": "alpha",  # 按字母顺序排序操作
        },
        # {{ Source: context7-mcp on 'drf-spectacular authentication configuration' }}
        # 全局Authorization请求头配置
        "APPEND_COMPONENTS": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer"}}},
        "SECURITY": [{"BearerAuth": []}],
    }

# IP白名单
IP_WHITE_LIST = config.get("ip_white_list", "127.0.0.1").split(",")


CLIENT_ID = config.get("client_id", "")
CLIENT_SECRET = config.get("client_secret", "")

# 缓存配置
CACHE_TIMEOUT = 60 * 15
ENABLE_CACHE = config.get("enable_cache", True)
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": config.get("cache_url", "redis://localhost:6379/1"),  # Redis连接信息
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}

SYSTEM_NAME = "智能处置运营管理"

# OAuth2 资源服务器安全配置（由gunicorn_config.py控制）
if config.get("oauth2_security_enabled", False):
    # HTTPS配置
    if config.get("oauth2_https_required", True):
        SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
        SECURE_SSL_REDIRECT = True

    # CORS配置
    if config.get("oauth2_cors_enabled", True):
        # 确保django-cors-headers已安装的情况下配置
        if "corsheaders" not in INSTALLED_APPS:
            INSTALLED_APPS.append("corsheaders")

        if "corsheaders.middleware.CorsMiddleware" not in MIDDLEWARE:
            MIDDLEWARE.insert(0, "corsheaders.middleware.CorsMiddleware")

        CORS_ALLOW_CREDENTIALS = config.get("cors_allow_credentials", True)
        cors_origins = config.get("cors_allowed_origins", "")
        if cors_origins:
            CORS_ALLOWED_ORIGINS = cors_origins.split(",")
        else:
            CORS_ALLOWED_ORIGINS = []
