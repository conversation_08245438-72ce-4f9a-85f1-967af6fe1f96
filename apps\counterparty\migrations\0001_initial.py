# Generated by Django 4.1.13 on 2025-07-22 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CreditorAddress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('address', models.TextField(blank=True, null=True, verbose_name='地址')),
                ('address_type', models.Char<PERSON>ield(blank=True, max_length=20, null=True, verbose_name='地址类型')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否主要联系方式')),
            ],
            options={
                'verbose_name': '债权人地址',
                'verbose_name_plural': '债权人地址',
                'db_table': 't_creditor_address',
            },
        ),
        migrations.CreateModel(
            name='CreditorEmail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='邮箱地址')),
                ('email_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='邮箱类型')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否主要联系方式')),
            ],
            options={
                'verbose_name': '债权人邮箱',
                'verbose_name_plural': '债权人邮箱',
                'db_table': 't_creditor_email',
            },
        ),
        migrations.CreateModel(
            name='CreditorPhone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='联系电话')),
                ('phone_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='电话类型')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否主要联系方式')),
            ],
            options={
                'verbose_name': '债权人联系电话',
                'verbose_name_plural': '债权人联系电话',
                'db_table': 't_creditor_phone',
            },
        ),
        migrations.CreateModel(
            name='DebtorAddress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('address', models.TextField(blank=True, null=True, verbose_name='地址')),
                ('address_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='地址类型')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否主要联系方式')),
            ],
            options={
                'verbose_name': '债务人地址',
                'verbose_name_plural': '债务人地址',
                'db_table': 't_debtor_address',
            },
        ),
        migrations.CreateModel(
            name='DebtorEmail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='邮箱地址')),
                ('email_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='邮箱类型')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否主要联系方式')),
            ],
            options={
                'verbose_name': '债务人邮箱',
                'verbose_name_plural': '债务人邮箱',
                'db_table': 't_debtor_email',
            },
        ),
        migrations.CreateModel(
            name='DebtorPhone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='联系电话')),
                ('phone_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='电话类型')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否主要联系方式')),
            ],
            options={
                'verbose_name': '债务人联系电话',
                'verbose_name_plural': '债务人联系电话',
                'db_table': 't_debtor_phone',
            },
        ),
        migrations.CreateModel(
            name='DebtorWechat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('wechat', models.CharField(blank=True, max_length=50, null=True, verbose_name='微信号')),
                ('wechat_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='微信类型')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否主要联系方式')),
            ],
            options={
                'verbose_name': '债务人微信',
                'verbose_name_plural': '债务人微信',
                'db_table': 't_debtor_wechat',
            },
        ),
        migrations.CreateModel(
            name='DebtorBasicInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('debtor_type', models.CharField(blank=True, choices=[('individual', '自然人'), ('legal_person', '法人'), ('other', '其他')], max_length=20, null=True, verbose_name='债务人类型')),
                ('debtor_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='债务人名称')),
                ('id_type', models.CharField(blank=True, choices=[('id_card', '身份证'), ('passport', '护照'), ('business_license', '营业执照')], max_length=20, null=True, verbose_name='证件类型')),
                ('id_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='证件号码')),
                ('addresses', models.ManyToManyField(blank=True, to='counterparty.debtoraddress', verbose_name='联系方式-地址')),
                ('emails', models.ManyToManyField(blank=True, to='counterparty.debtoremail', verbose_name='联系方式-邮箱')),
                ('phones', models.ManyToManyField(blank=True, to='counterparty.debtorphone', verbose_name='联系方式-电话')),
                ('wechats', models.ManyToManyField(blank=True, to='counterparty.debtorwechat', verbose_name='联系方式-微信')),
            ],
            options={
                'verbose_name': '债务人基本信息',
                'verbose_name_plural': '债务人基本信息',
                'db_table': 't_debtor_basic_info',
            },
        ),
        migrations.CreateModel(
            name='CreditorBasicInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('creditor_type', models.CharField(blank=True, choices=[('bank', '银行'), ('financial_asset_management', '金融资产管理公司'), ('consumer_finance', '消费金融公司'), ('auto_finance', '汽车金融公司'), ('financial_leasing', '金融租赁公司'), ('small_loan', '小额贷款公司'), ('financing_guarantee', '融资担保公司'), ('financing_leasing', '融资租赁公司'), ('local_asset_management', '地方资产管理公司'), ('lending_institution', '助贷机构'), ('insurance', '保险公司'), ('technology', '科技公司'), ('individual', '自然人'), ('other', '其他')], max_length=50, null=True, verbose_name='债权人类型')),
                ('creditor_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='债权人名称')),
                ('id_type', models.CharField(blank=True, choices=[('id_card', '身份证'), ('passport', '护照'), ('business_license', '营业执照')], max_length=20, null=True, verbose_name='证件类型')),
                ('id_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='证件号码')),
                ('addresses', models.ManyToManyField(blank=True, to='counterparty.creditoraddress', verbose_name='联系方式-地址')),
                ('emails', models.ManyToManyField(blank=True, to='counterparty.creditoremail', verbose_name='联系方式-邮箱')),
                ('phones', models.ManyToManyField(blank=True, to='counterparty.creditorphone', verbose_name='联系方式-电话')),
            ],
            options={
                'verbose_name': '债权人基本信息',
                'verbose_name_plural': '债权人基本信息',
                'db_table': 't_creditor_basic_info',
            },
        ),
    ]
