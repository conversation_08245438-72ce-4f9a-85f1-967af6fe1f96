#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时脚本：为 Feedback 模型生成随机测试数据
执行方式：python generate_feedback_data.py
"""

import os
import sys
import django
import random
from datetime import datetime, timedelta
from django.utils import timezone

# 设置Django环境 - 修正路径为项目根目录
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ops_management.settings')
django.setup()

from apps.feedback.models import Feedback


def generate_phone_number():
    """生成随机手机号码"""
    prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                '150', '151', '152', '153', '155', '156', '157', '158', '159',
                '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
    prefix = random.choice(prefixes)
    suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
    return prefix + suffix


def generate_case_number():
    """生成随机案件编号：调解中心编号+发起日期+当日案件编号"""
    # 调解中心编号
    center_code = "GZTJ"
    
    # 发起日期：年月日
    year = random.choice([2023, 2024])
    month = random.randint(1, 12)
    day = random.randint(1, 28)  # 使用1-28避免月份天数问题
    date_str = f"{year}{month:02d}{day:02d}"
    
    # 当日案件编号：4位数字
    daily_seq = random.randint(1, 9999)
    seq_str = f"{daily_seq:04d}"
    
    return f"{center_code}{date_str}{seq_str}"


def generate_description(feedback_type, category):
    """根据反馈类型和类别生成相应的描述"""
    suggestions = {
        'process_optimization': [
            "建议简化办事流程，减少重复材料提交环节，提高办事效率。",
            "希望能够优化在线申请流程，增加进度查询功能。",
            "建议整合各部门审批环节，实现一站式服务。",
        ],
        'function_improvement': [
            "建议增加手机APP功能，方便随时查看案件进度。",
            "希望网站能够支持在线缴费功能，避免跑腿。",
            "建议增加短信通知功能，及时告知案件状态变化。",
        ],
        'service_praise': [
            "工作人员态度很好，处理问题很及时，值得表扬。",
            "调解员专业素质高，帮助我们圆满解决了纠纷，非常感谢。",
            "整个服务流程很规范，工作效率很高，给个好评。",
        ],
        'other_suggestion': [
            "建议增加停车位，方便当事人办事。",
            "希望能够延长服务时间，照顾上班族的需求。",
            "建议在大厅增加饮水设备和休息座椅。",
        ]
    }
    
    complaints = {
        'service_attitude': [
            "工作人员态度冷漠，回答问题不耐烦，希望改进服务态度。",
            "接待人员说话语气较重，让人感觉不舒服。",
            "咨询问题时工作人员显得很不耐烦，服务态度需要改善。",
        ],
        'processing_time': [
            "案件处理时间过长，已经超过承诺期限，影响解决效率。",
            "申请材料提交后一直没有回复，等待时间太长。",
            "调解安排时间拖延，希望能够提高处理速度。",
        ],
        'solution_inappropriate': [
            "提出的调解方案不够合理，没有充分考虑双方利益。",
            "处理结果偏向一方，感觉不够公正客观。",
            "解决方案缺乏可操作性，难以实际执行。",
        ],
        'system_technical': [
            "网站经常打不开，影响在线办事体验。",
            "上传文件总是失败，系统功能不稳定。",
            "系统操作界面不友好，使用起来很困难。",
        ],
        'other_complaint': [
            "办事大厅环境嘈杂，影响正常沟通交流。",
            "等待时间过长，没有合理的排队叫号系统。",
            "相关收费标准不够透明，希望公开详细收费项目。",
        ]
    }
    
    if feedback_type == 'suggestion':
        return random.choice(suggestions.get(category, suggestions['other_suggestion']))
    else:
        return random.choice(complaints.get(category, complaints['other_complaint']))


def generate_handler_info():
    """生成处理人相关信息"""
    handlers = ['张明', '李华', '王芳', '刘强', '陈静', '杨勇', '赵丽', '孙伟']
    
    # 30% 概率已被处理
    if random.random() < 0.3:
        handler = random.choice(handlers)
        handle_time = timezone.now() - timedelta(days=random.randint(1, 30))
        
        handle_results = [
            "已联系反馈人，问题得到妥善解决。",
            "根据建议优化了相关流程，感谢反馈。",
            "已安排专人跟进处理，确保问题彻底解决。",
            "经调查核实，已对相关人员进行培训改进。",
            "已采纳建议并实施改进措施。"
        ]
        handle_result = random.choice(handle_results)
        
        return handler, handle_time, handle_result
    
    return None, None, None


def create_feedback_data():
    """创建 20 条 Feedback 随机数据"""
    print("开始生成 Feedback 模型随机数据...")
    
    # 清理可能存在的测试数据（可选）
    # Feedback.objects.filter(description__contains='测试数据').delete()
    
    feedback_list = []
    
    for i in range(20):
        # 随机选择反馈类型
        feedback_type = random.choice(['suggestion', 'complaint'])
        
        # 根据反馈类型选择对应的类别
        if feedback_type == 'suggestion':
            category = random.choice([choice[0] for choice in Feedback.SUGGESTION_CATEGORY_CHOICES])
        else:
            category = random.choice([choice[0] for choice in Feedback.COMPLAINT_CATEGORY_CHOICES])
        
        # 生成其他字段数据
        case_number = generate_case_number() if random.random() < 0.6 else None  # 60% 概率有案件编号
        description = generate_description(feedback_type, category)
        phone_number = generate_phone_number()
        process_status = random.choice([choice[0] for choice in Feedback.PROCESS_STATUS_CHOICES])
        
        # 生成处理信息
        handler, handle_time, handle_result = generate_handler_info()
        
        feedback = Feedback(
            feedback_type=feedback_type,
            case_number=case_number,
            category=category,
            description=description,
            phone_number=phone_number,
            process_status=process_status,
            handler=handler,
            handle_time=handle_time,
            handle_result=handle_result
        )
        
        feedback_list.append(feedback)
        print(f"生成数据 {i+1}/20: {feedback.get_feedback_type_display()} - {feedback.get_category_display()}")
    
    # 批量创建数据
    try:
        Feedback.objects.bulk_create(feedback_list)
        print(f"\n✅ 成功创建 {len(feedback_list)} 条 Feedback 数据！")
        
        # 显示统计信息
        total_count = Feedback.objects.count()
        suggestion_count = Feedback.objects.filter(feedback_type='suggestion').count()
        complaint_count = Feedback.objects.filter(feedback_type='complaint').count()
        
        print(f"数据库中 Feedback 总数: {total_count}")
        print(f"意见建议: {suggestion_count} 条")
        print(f"服务投诉: {complaint_count} 条")
        
    except Exception as e:
        print(f"❌ 数据创建失败: {str(e)}")


if __name__ == '__main__':
    create_feedback_data() 