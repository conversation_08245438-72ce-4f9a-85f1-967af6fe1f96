# Generated by Django 4.1.13 on 2025-07-22 12:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('counterparty', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='VoiceCallRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('called_number', models.CharField(help_text='被叫方的手机号码或固定电话号码', max_length=20, verbose_name='被叫号码')),
                ('caller_number', models.CharField(help_text='外呼系统使用的主叫号码', max_length=20, verbose_name='主叫号码')),
                ('call_status', models.CharField(choices=[('not_connected', '未接通'), ('connected', '已接通'), ('busy', '忙线'), ('power_off', '关机'), ('no_answer', '无人接听'), ('invalid_number', '无效号码'), ('rejected', '拒接'), ('failed', '呼叫失败')], default='not_connected', help_text='本次外呼的结果状态', max_length=20, verbose_name='呼叫状态')),
                ('call_duration', models.PositiveIntegerField(default=0, help_text='实际通话时长，单位为秒，未接通时为0', verbose_name='通话时长(秒)')),
                ('call_start_time', models.DateTimeField(help_text='发起外呼的具体时间', verbose_name='呼叫开始时间')),
                ('call_end_time', models.DateTimeField(blank=True, help_text='呼叫结束的具体时间，未接通时可能为空', null=True, verbose_name='呼叫结束时间')),
                ('recording_file_path', models.CharField(blank=True, help_text='通话录音文件的存储路径，未接通时为空', max_length=500, null=True, verbose_name='录音文件路径')),
                ('task_batch_id', models.CharField(blank=True, help_text='外呼任务或批次的唯一标识符', max_length=100, null=True, verbose_name='外呼任务批次号')),
                ('call_result_notes', models.TextField(blank=True, help_text='记录外呼过程中的重要信息、客户反馈或处理结果', null=True, verbose_name='外呼结果备注')),
                ('creditor', models.ForeignKey(blank=True, help_text='代表进行外呼的债权人信息', null=True, on_delete=django.db.models.deletion.SET_NULL, to='counterparty.creditorbasicinfo', verbose_name='关联债权人')),
                ('debtor', models.ForeignKey(blank=True, help_text='外呼目标的债务人信息', null=True, on_delete=django.db.models.deletion.SET_NULL, to='counterparty.debtorbasicinfo', verbose_name='关联债务人')),
            ],
            options={
                'verbose_name': '语音外呼记录',
                'verbose_name_plural': '语音外呼记录',
                'db_table': 't_voice_call_record',
                'ordering': ['-call_start_time'],
            },
        ),
        migrations.CreateModel(
            name='SmsRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('recipient_phone', models.CharField(help_text='短信接收方的手机号码', max_length=20, verbose_name='接收手机号')),
                ('sms_content', models.TextField(help_text='实际发送的短信文本内容', verbose_name='短信内容')),
                ('sms_status', models.CharField(choices=[('pending', '待发送'), ('sending', '发送中'), ('sent_success', '发送成功'), ('sent_failed', '发送失败')], default='pending', help_text='短信发送的当前状态', max_length=20, verbose_name='发送状态')),
                ('send_time', models.DateTimeField(blank=True, help_text='短信实际发送的时间戳', null=True, verbose_name='发送时间')),
                ('delivery_time', models.DateTimeField(blank=True, help_text='短信成功送达到接收方的时间戳', null=True, verbose_name='送达时间')),
                ('task_batch_id', models.CharField(blank=True, help_text='批量发送短信时的任务批次唯一标识符', max_length=100, null=True, verbose_name='任务批次号')),
                ('failure_reason', models.TextField(blank=True, help_text='短信发送或送达失败时的具体原因描述', null=True, verbose_name='失败原因')),
                ('sms_type', models.CharField(choices=[('notification', '通知短信'), ('verification', '验证码短信'), ('reminder', '提醒短信'), ('collection', '催收短信'), ('other', '其他')], default='notification', help_text='短信的业务类型分类', max_length=20, verbose_name='短信类型')),
                ('creditor', models.ForeignKey(blank=True, help_text='代表发送短信的债权人信息', null=True, on_delete=django.db.models.deletion.SET_NULL, to='counterparty.creditorbasicinfo', verbose_name='关联债权人')),
                ('debtor', models.ForeignKey(blank=True, help_text='短信目标的债务人信息', null=True, on_delete=django.db.models.deletion.SET_NULL, to='counterparty.debtorbasicinfo', verbose_name='关联债务人')),
            ],
            options={
                'verbose_name': '短信发送记录',
                'verbose_name_plural': '短信发送记录',
                'db_table': 't_sms_record',
            },
        ),
    ]
