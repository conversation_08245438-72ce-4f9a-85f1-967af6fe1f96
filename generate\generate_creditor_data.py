#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : generate_creditor_data.py
<AUTHOR> AI Assistant
@Date     : 2025/07/14
@File_Desc: 债权人基本信息测试数据生成脚本
"""

import os
import sys
import django
import random
from django.db import transaction

# 设置Django环境 - 修正路径为项目根目录
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ops_management.settings')
django.setup()

from apps.counterparty.models import CreditorBasicInfo, CreditorPhone, CreditorEmail, CreditorAddress

# 中文姓名和地名数据
CHINESE_SURNAMES = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
CHINESE_GIVEN_NAMES = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞']
CHINESE_CITIES = ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '重庆', '天津', '苏州', '西安', '长沙', '沈阳', '青岛', '郑州', '大连', '东莞', '宁波']
CHINESE_PROVINCES = ['北京', '上海', '天津', '重庆', '河北', '山西', '辽宁', '吉林', '黑龙江', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南', '湖北', '湖南', '广东', '海南', '四川', '贵州', '云南', '陕西', '甘肃', '青海', '台湾', '内蒙古', '广西', '西藏', '宁夏', '新疆', '香港', '澳门']


class CreditorDataGenerator:
    """债权人测试数据生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.phone_types = ['手机', '座机', '传真', '客服热线']
        self.email_types = ['工作邮箱', '个人邮箱', '客服邮箱', '官方邮箱']
        self.address_types = ['注册地址', '办公地址', '通讯地址', '法定地址']
        
        # 机构名称后缀
        self.institution_suffixes = [
            '银行', '银行股份有限公司', '商业银行', '农村商业银行',
            '金融资产管理有限公司', '资产管理公司',
            '消费金融有限公司', '消费金融股份有限公司',
            '汽车金融有限公司', '汽车金融股份有限公司',
            '金融租赁有限公司', '金融租赁股份有限公司',
            '小额贷款有限公司', '小额贷款股份有限公司',
            '融资担保有限公司', '融资担保股份有限公司',
            '融资租赁有限公司', '融资租赁股份有限公司',
            '保险股份有限公司', '财产保险股份有限公司',
            '科技有限公司', '信息技术有限公司'
        ]
    
    def generate_chinese_name(self):
        """生成中文姓名"""
        surname = random.choice(CHINESE_SURNAMES)
        given_name = random.choice(CHINESE_GIVEN_NAMES)
        if random.random() < 0.3:  # 30%概率生成双字名
            given_name += random.choice(CHINESE_GIVEN_NAMES)
        return surname + given_name

    def generate_phone_number(self):
        """生成中国手机号码"""
        prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                   '150', '151', '152', '153', '155', '156', '157', '158', '159',
                   '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
        prefix = random.choice(prefixes)
        suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        return prefix + suffix

    def generate_email(self):
        """生成邮箱地址"""
        domains = ['qq.com', '163.com', '126.com', 'sina.com', 'gmail.com', 'hotmail.com']
        username = ''.join([chr(random.randint(97, 122)) for _ in range(random.randint(6, 12))])
        domain = random.choice(domains)
        return f"{username}@{domain}"

    def generate_address(self):
        """生成中文地址"""
        province = random.choice(CHINESE_PROVINCES)
        city = random.choice(CHINESE_CITIES)
        district = f"{random.choice(['东', '西', '南', '北', '中'])}区"
        street = f"{random.choice(['建设', '人民', '中山', '解放', '和平', '友谊', '胜利'])}路{random.randint(1, 999)}号"
        return f"{province}省{city}市{district}{street}"

    def generate_id_number(self, id_type):
        """根据证件类型生成对应的证件号码"""
        if id_type == 'id_card':
            # 生成18位身份证号码
            area_code = f"{random.randint(110000, 659000):06d}"
            birth_date = f"{random.randint(1970, 2000):04d}{random.randint(1, 12):02d}{random.randint(1, 28):02d}"
            sequence = f"{random.randint(1, 999):03d}"
            check_digit = random.randint(0, 9)
            return area_code + birth_date + sequence + str(check_digit)
        elif id_type == 'passport':
            # 生成护照号码格式：E + 8位数字
            return f"E{random.randint(10000000, 99999999)}"
        elif id_type == 'business_license':
            # 生成统一社会信用代码：18位
            prefix = random.choice(['91', '92', '93'])
            return prefix + ''.join([str(random.randint(0, 9)) for _ in range(16)])
        return None
    
    def generate_creditor_name(self, creditor_type):
        """根据债权人类型生成相应的名称"""
        if creditor_type == 'individual':
            # 自然人使用中文姓名
            return self.generate_chinese_name()
        else:
            # 机构使用地名+机构类型
            city = random.choice(CHINESE_CITIES)
            suffix = random.choice(self.institution_suffixes)
            return f"{city}{suffix}"
    
    def generate_phone_data(self):
        """生成电话数据"""
        phones = []
        phone_count = random.randint(1, 3)  # 每个债权人1-3个电话
        
        for i in range(phone_count):
            phone_data = {
                'phone': self.generate_phone_number(),
                'phone_type': random.choice(self.phone_types),
                'is_primary': i == 0  # 第一个设为主要联系方式
            }
            phones.append(phone_data)
        
        return phones
    
    def generate_email_data(self):
        """生成邮箱数据"""
        emails = []
        email_count = random.randint(1, 2)  # 每个债权人1-2个邮箱
        
        for i in range(email_count):
            email_data = {
                'email': self.generate_email(),
                'email_type': random.choice(self.email_types),
                'is_primary': i == 0  # 第一个设为主要联系方式
            }
            emails.append(email_data)
        
        return emails
    
    def generate_address_data(self):
        """生成地址数据"""
        addresses = []
        address_count = random.randint(1, 2)  # 每个债权人1-2个地址
        
        for i in range(address_count):
            address_data = {
                'address': self.generate_address(),
                'address_type': random.choice(self.address_types),
                'is_primary': i == 0  # 第一个设为主要联系方式
            }
            addresses.append(address_data)
        
        return addresses
    
    def create_creditor_with_contacts(self):
        """创建债权人及其联系方式"""
        # 随机选择债权人类型
        creditor_type = random.choice([choice[0] for choice in CreditorBasicInfo.CREDITOR_TYPE_CHOICES])
        
        # 随机选择证件类型
        id_type = random.choice([choice[0] for choice in CreditorBasicInfo.ID_TYPE_CHOICES])
        
        # 生成债权人基本信息
        creditor_data = {
            'creditor_type': creditor_type,
            'creditor_name': self.generate_creditor_name(creditor_type),
            'id_type': id_type,
            'id_number': self.generate_id_number(id_type),
            'created_by': 1,  # 假设系统用户ID为1
            'updated_by': 1
        }
        
        # 创建债权人记录
        creditor = CreditorBasicInfo.objects.create(**creditor_data)
        
        # 创建并关联电话记录
        phone_data_list = self.generate_phone_data()
        for phone_data in phone_data_list:
            phone = CreditorPhone.objects.create(**phone_data)
            creditor.phones.add(phone)
        
        # 创建并关联邮箱记录
        email_data_list = self.generate_email_data()
        for email_data in email_data_list:
            email = CreditorEmail.objects.create(**email_data)
            creditor.emails.add(email)
        
        # 创建并关联地址记录
        address_data_list = self.generate_address_data()
        for address_data in address_data_list:
            address = CreditorAddress.objects.create(**address_data)
            creditor.addresses.add(address)
        
        return creditor
    
    def generate_test_data(self, count=20):
        """生成指定数量的测试数据"""
        print(f"开始生成 {count} 条债权人测试数据...")
        
        created_count = 0
        
        try:
            with transaction.atomic():  # 使用数据库事务确保数据一致性
                for i in range(count):
                    try:
                        creditor = self.create_creditor_with_contacts()
                        created_count += 1
                        print(f"✓ 已创建第 {created_count} 条记录: {creditor.creditor_name}")
                    except Exception as e:
                        print(f"✗ 创建第 {i+1} 条记录时出错: {str(e)}")
                        continue
                
                print(f"\n🎉 数据生成完成！成功创建 {created_count} 条债权人记录")
                
        except Exception as e:
            print(f"❌ 数据生成过程中发生错误: {str(e)}")
            return False
        
        return True
    
    def clean_existing_data(self):
        """清理现有测试数据（可选）"""
        print("正在清理现有债权人测试数据...")
        
        try:
            with transaction.atomic():
                # 删除所有债权人记录（会级联删除关联的联系方式）
                deleted_count = CreditorBasicInfo.objects.all().count()
                CreditorBasicInfo.objects.all().delete()
                
                # 清理孤立的联系方式记录
                CreditorPhone.objects.filter(creditorbasicinfo__isnull=True).delete()
                CreditorEmail.objects.filter(creditorbasicinfo__isnull=True).delete()
                CreditorAddress.objects.filter(creditorbasicinfo__isnull=True).delete()
                
                print(f"✓ 已清理 {deleted_count} 条现有记录")
                
        except Exception as e:
            print(f"❌ 清理数据时发生错误: {str(e)}")
            return False
        
        return True


def main():
    """主函数"""
    print("=" * 60)
    print("债权人基本信息测试数据生成脚本")
    print("=" * 60)
    
    generator = CreditorDataGenerator()
    
    # 询问是否清理现有数据
    clean_choice = input("是否清理现有债权人数据？(y/N): ").lower().strip()
    if clean_choice in ['y', 'yes']:
        if not generator.clean_existing_data():
            return
    
    # 询问生成数据数量
    try:
        count = input("请输入要生成的数据条数 (默认20): ").strip()
        count = int(count) if count else 20
        if count <= 0:
            print("❌ 数据条数必须大于0")
            return
    except ValueError:
        print("❌ 请输入有效的数字")
        return
    
    # 生成测试数据
    success = generator.generate_test_data(count)
    
    if success:
        print("\n✅ 脚本执行完成！")
    else:
        print("\n❌ 脚本执行失败！")


if __name__ == '__main__':
    main()
