#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : permission_helper.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc: 权限辅助工具模块，包含各种自定义权限类和权限验证装饰器
"""

from functools import wraps

import requests
from django.conf import settings
from ipware import get_client_ip
from rest_framework import permissions

from ops_management.settings import AUTH_SERVER_URL
from utils.ajax_result import AjaxResult




class MyPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.user and request.user.is_authenticated:
            # 拦截逻辑：检查用户名前缀，拒绝以"wx_"开头的用户访问
            # 目的：基于用户名前缀进行访问控制，提高系统安全性
            if hasattr(request.user, 'username') and request.user.username.startswith('wx_'):
                return False
            return True
        client_ip, _ = get_client_ip(request)
        return client_ip in settings.IP_WHITE_LIST


class WechatBasicPermission(permissions.BasePermission):
    """
    微信小程序基础权限类

    用途：专门用于微信小程序端的基础API请求授权
    授权规则：
    1. 用户必须已认证（is_authenticated）
    2. 用户名必须以 "wx_" 前缀开头
    3. 不符合条件的用户直接拒绝访问

    使用场景：微信小程序相关的基础API端点，无需人脸核身验证
    """

    def has_permission(self, request, view):
        # 检查用户是否已认证
        if not (request.user and request.user.is_authenticated):
            return False

        # 检查用户名是否以 "wx_" 开头（微信小程序用户标识）
        # 使用 hasattr 确保用户对象具有 username 属性，提高代码健壮性
        if hasattr(request.user, 'username') and request.user.username.startswith('wx_'):
            return True

        # 不符合微信小程序用户条件，拒绝访问
        return False


class WechatFaceAuthPermission(permissions.BasePermission):
    """
    微信小程序人脸核身权限类

    实现三重验证机制：
    1. 用户认证状态验证：确保用户已通过Django认证系统
    2. 微信用户名格式验证：用户名必须以"wx_"前缀开头
    3. 人脸核身验证：验证用户的人脸认证状态，必要时进行同步认证处理

    使用场景：需要严格身份验证和人脸核身的微信小程序API端点
    """

    def has_permission(self, request, view):
        """
        权限验证主方法

        Args:
            request: HTTP请求对象
            view: 视图对象

        Returns:
            bool: True表示权限验证通过，False表示权限验证失败
        """
        # 第一重验证：用户认证状态验证
        if not (request.user and request.user.is_authenticated):
            return False

        # 第二重验证：微信用户名格式验证
        if not (hasattr(request.user, 'username') and request.user.username.startswith('wx_')):
            return False

        # 第三重验证：人脸核身验证
        try:
            # 导入SystemUser模型（延迟导入避免循环依赖）
            from apps.user.models import SystemUser

            # 根据用户名查询SystemUser记录
            username = request.user.username
            try:
                system_user = SystemUser.objects.get(username=username)
            except SystemUser.DoesNotExist:
                return False
            except Exception:
                return False

            # 检查人脸核身结果
            if system_user.detect_auth_result is True:
                return True

            # 人脸核身结果不为True，尝试同步处理
            # 检查是否有biz_token用于同步处理
            if not system_user.biz_token:
                return False

            # 同步调用人脸认证处理函数
            try:
                # 获取认证令牌
                auth_token = request.headers.get("Authorization", "")
                if not auth_token:
                    return False

                # 导入必要的函数（延迟导入避免循环依赖）
                from utils.tencent_faceid_utils import process_face_auth_result
                from apps.user.tasks import sync_auth_user_local

                # 处理人脸认证结果
                process_face_auth_result(auth_token, system_user.biz_token)

                # 同步用户数据到本地
                sync_auth_user_local()

                # 重新查询SystemUser获取更新后的结果
                system_user.refresh_from_db()

                # 检查更新后的人脸核身结果
                if system_user.detect_auth_result is True:
                    return True
                else:
                    return False

            except Exception:
                return False

        except Exception:
            return False


def request_auth_has_perm(request, per):
    auth_header = request.headers.get("Authorization")
    url = f"{AUTH_SERVER_URL}/has_perm/"
    headers = {"Authorization": auth_header}
    querystring = {"permission": per}
    response = requests.get(url, params=querystring, headers=headers)
    return response.json().get("data", {}).get("has_permission", False)


def permission_required(per=None):
    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            if isinstance(per, (list, tuple)):
                # 如果是数组,只要有一个权限通过即可
                for permission in per:
                    if request_auth_has_perm(request, permission):
                        return func(self, request, *args, **kwargs)
                return AjaxResult.forbidden()
            else:
                # 单个权限的情况
                if request_auth_has_perm(request, per):
                    return func(self, request, *args, **kwargs)
                else:
                    return AjaxResult.forbidden()

        return wrapper

    return decorator
