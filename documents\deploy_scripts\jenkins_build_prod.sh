#!/bin/bash

set -e

# 此脚本用于在WSL（Ubuntu）构建后端镜像（生产），并保存镜像文件，镜像文件用于在华泰民部署，镜像架构为：x86_64

# 挂载系统目录
SHARE_MOUNT="/mnt/c/Users/<USER>/Desktop/华泰民更新包/"

# 定义常量
TAG=$(date +%Y%m%d)
IMAGE_NAME="ops_management_htm:$TAG"
DEPLOY_PATH="$SHARE_MOUNT/华泰民$TAG"
IMAGE_TAR="ops_management_htm.tar"

# 准备工作区
echo "当前工作目录: $(pwd)"

# 创建必要的目录（如果不存在）
sudo mkdir -p "$DEPLOY_PATH"

# 函数：构建镜像
build_image() {
    sudo docker build -t "$IMAGE_NAME" .
}

# 函数：删除所有标签为 "<none>" 的 Docker 镜像
remove_none_images() {
    none_images=$(sudo docker images | grep "^<none>" | awk '{print $3}')
    if [ -n "$none_images" ]; then
        sudo docker rmi $none_images
    else
        echo "No <none> tagged images to remove."
    fi
}

# 函数：保存镜像
save_image() {
    sudo docker save -o "$DEPLOY_PATH/$IMAGE_TAR" "$IMAGE_NAME"
}

# 函数：复制运行脚本
copy_run_script() {
    sudo cp "documents/deploy_scripts/ops_management_htm_run.sh" "$DEPLOY_PATH/"
    sudo cp "documents/deploy_scripts/deploy_frontend_for_ops_management.sh" "$DEPLOY_PATH/"
    echo "运行脚本已复制至 $DEPLOY_PATH"
}

# 执行步骤
build_image
remove_none_images
save_image
copy_run_script

echo "镜像已成功构建并保存至 $DEPLOY_PATH/$IMAGE_TAR"