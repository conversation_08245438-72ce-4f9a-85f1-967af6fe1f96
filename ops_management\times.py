#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : times.py
<AUTHOR> JT_DA
@Date     : 2025/07/01
@File_Desc:
"""

import logging
import os
from datetime import datetime
from typing import Optional

from django.conf import settings
from django.core.management import call_command
from django.utils import timezone
from oauth2_provider.models import AccessToken, RefreshToken

from ops_management.celery import app


# 自动清除过期的access_token和refresh_token
@app.task()
def clear_token():
    """
    清除过期的access_token和refresh_token
    """
    logging.info("start clear token task")
    now = timezone.now()

    # 清除过期的access_token
    expired_access_tokens = AccessToken.objects.filter(expires__lt=now)
    expired_access_tokens.delete()

    # 清除过期的refresh_token

    # expired_refresh_tokens = RefreshToken.objects.filter(expires__lt=now)
    # expired_refresh_tokens.delete()

    logging.info("clear token task finished")

    return "success"


@app.task()
def auto_backup_database() -> Optional[str]:
    """
    This function performs an automatic backup of the database.

    Returns:
        Optional[str]: A string indicating the success of the backup operation.
    """
    # Get the current date and time
    logging.info("start backup database task")
    now = datetime.now()

    # Set the backup file name, e.g. backup_2023-07-28_00-00-00.json
    backup_file_name = f"backup_{now.strftime('%Y-%m-%d_%H-%M-%S')}.json"

    # Set the backup save path
    backup_path = settings.BACKUP_DIR

    # Construct the absolute path of the backup file
    backup_file_path = os.path.join(backup_path, backup_file_name)

    # Call the dumpdata command to export the database data to the backup file
    with open(backup_file_path, "w") as backup_file:
        call_command("dumpdata", stdout=backup_file)

    logging.info("backup database task finished")

    return "success"
