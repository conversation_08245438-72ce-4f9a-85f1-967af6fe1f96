from django.db import models
from ops_management.base_model import BaseModel


class Feedback(BaseModel):
    """
    投诉建议模型
    """
    FEEDBACK_TYPE_CHOICES = [
        ('suggestion', '意见建议'),
        ('complaint', '服务投诉'),
    ]
    
    # 意见建议的具体类别
    SUGGESTION_CATEGORY_CHOICES = [
        ('process_optimization', '流程优化建议'),
        ('function_improvement', '功能改进建议'),
        ('service_praise', '服务表扬'),
        ('other_suggestion', '其他建议'),
    ]
    
    # 服务投诉的具体类别
    COMPLAINT_CATEGORY_CHOICES = [
        ('service_attitude', '服务态度问题'),
        ('processing_time', '处理时间过长'),
        ('solution_inappropriate', '方案不合适'),
        ('system_technical', '系统技术问题'),
        ('other_complaint', '其他投诉'),
    ]
    
    PROCESS_STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'), 
        ('resolved', '已解决'),
        ('closed', '已关闭'),
    ]
    
    feedback_type = models.CharField(max_length=20, choices=FEEDBACK_TYPE_CHOICES, verbose_name='反馈类型')
    case_number = models.CharField(max_length=100, blank=True, null=True, help_text='可选，关联的调解案件编号', verbose_name='关联调解案件编号')
    category = models.CharField(max_length=30, help_text='根据反馈类型选择对应的具体类别', verbose_name='具体类别')
    description = models.TextField(verbose_name='详细描述')
    phone_number = models.CharField(max_length=20, help_text='反馈人的手机号码', verbose_name='联系方式(手机号)')
    process_status = models.CharField(max_length=20, choices=PROCESS_STATUS_CHOICES, default='pending', verbose_name='处理状态')
    
    # 处理信息
    handler = models.CharField(max_length=50, blank=True, null=True, verbose_name='处理人')
    handle_time = models.DateTimeField(blank=True, null=True, verbose_name='处理时间')
    handle_result = models.TextField(blank=True, null=True, verbose_name='处理结果')
    
    class Meta:
        verbose_name = '投诉建议'
        verbose_name_plural = '投诉建议'
        db_table = 't_feedback'
    
    def __str__(self):
        return f'{self.get_feedback_type_display()}: {self.get_category_display()}'
        
    def get_category_display(self):
        """获取类别的显示名称"""
        if self.feedback_type == 'suggestion':
            category_dict = dict(self.SUGGESTION_CATEGORY_CHOICES)
        else:  # complaint
            category_dict = dict(self.COMPLAINT_CATEGORY_CHOICES)
        return category_dict.get(self.category, self.category)
    
    def clean(self):
        """验证类别与反馈类型是否匹配"""
        from django.core.exceptions import ValidationError
        
        suggestion_categories = [choice[0] for choice in self.SUGGESTION_CATEGORY_CHOICES]
        complaint_categories = [choice[0] for choice in self.COMPLAINT_CATEGORY_CHOICES]
        
        if self.feedback_type == 'suggestion' and self.category not in suggestion_categories:
            raise ValidationError('意见建议类型的类别选择不正确')
        elif self.feedback_type == 'complaint' and self.category not in complaint_categories:
            raise ValidationError('服务投诉类型的类别选择不正确') 