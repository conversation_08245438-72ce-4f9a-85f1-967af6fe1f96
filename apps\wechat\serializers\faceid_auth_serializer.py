#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : faceid_auth_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/07/24
@File_Desc: 腾讯云FaceID人脸核身认证序列化器
"""

from rest_framework import serializers
from utils.validator_helper import validate_id_card


class FaceIDAuthSerializer(serializers.Serializer):
    """
    腾讯云FaceID人脸核身认证序列化器

    用于验证人脸核身接口的请求参数，包括身份证号码格式验证、姓名非空验证和微信openid验证。
    确保传入腾讯云FaceID API的参数符合要求，提高认证成功率。
    注意：openid参数暂不传递给腾讯云API，为后续功能扩展预留。
    """

    openid = serializers.CharField(
        required=True,
        help_text="微信小程序用户唯一标识符，用于用户身份关联",
        error_messages={
            'required': 'openid不能为空',
            'blank': 'openid不能为空'
        }
    )

    id_card = serializers.CharField(
        max_length=18,
        min_length=18,
        required=True,
        help_text="18位身份证号码，必须符合中国大陆身份证格式规范",
        error_messages={
            'required': '身份证号码不能为空',
            'max_length': '身份证号码必须为18位',
            'min_length': '身份证号码必须为18位',
            'blank': '身份证号码不能为空'
        }
    )

    name = serializers.CharField(
        max_length=50,
        min_length=2,
        required=True,
        help_text="真实姓名，长度在2-50个字符之间",
        error_messages={
            'required': '姓名不能为空',
            'max_length': '姓名长度不能超过50个字符',
            'min_length': '姓名长度不能少于2个字符',
            'blank': '姓名不能为空'
        }
    )

    def validate_openid(self, value):
        """
        验证微信openid格式是否合法

        检查openid是否为空、是否包含非法字符等，确保openid符合基本要求。
        去除首尾空格，保证数据的一致性。
        注意：此参数暂不传递给腾讯云API，为后续功能扩展预留。

        Args:
            value: 微信openid字符串

        Returns:
            str: 验证通过并处理后的openid

        Raises:
            serializers.ValidationError: 当openid格式不正确时抛出
        """
        # 去除首尾空格
        value = value.strip()

        # 检查是否为空
        if not value:
            raise serializers.ValidationError("openid不能为空")

        # 基本格式验证：openid通常由字母、数字、下划线、短横线组成
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', value):
            raise serializers.ValidationError("openid格式不正确，只能包含字母、数字、下划线和短横线")

        return value

    def validate_id_card(self, value):
        """
        验证身份证号码格式是否合法
        
        使用 utils.validator_helper.validate_id_card() 函数进行专业的身份证号码验证，
        包括长度、地区码、出生日期、校验码等全面检查。
        
        Args:
            value: 身份证号码字符串
            
        Returns:
            str: 验证通过的身份证号码
            
        Raises:
            serializers.ValidationError: 当身份证号码格式不正确时抛出
        """
        if not validate_id_card(value):
            raise serializers.ValidationError("身份证号码格式不正确，请检查输入")
        return value
    
    def validate_name(self, value):
        """
        验证姓名格式是否合法
        
        检查姓名是否为空、是否包含非法字符等，确保姓名符合基本要求。
        去除首尾空格，保证数据的一致性。
        
        Args:
            value: 姓名字符串
            
        Returns:
            str: 验证通过并处理后的姓名
            
        Raises:
            serializers.ValidationError: 当姓名格式不正确时抛出
        """
        # 去除首尾空格
        value = value.strip()
        
        # 检查是否为空
        if not value:
            raise serializers.ValidationError("姓名不能为空")
        
        # 检查是否包含数字（中文姓名通常不包含数字）
        if any(char.isdigit() for char in value):
            raise serializers.ValidationError("姓名不能包含数字")
        
        return value
