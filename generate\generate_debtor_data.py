#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : generate_debtor_data.py
<AUTHOR> JT_DA
@Date     : 2025/07/14
@File_Desc: 债务人基本信息测试数据生成脚本
"""

import os
import sys
import django
import random
from django.db import transaction

# 设置Django环境 - 修正路径为项目根目录
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ops_management.settings')
django.setup()

from apps.counterparty.models import (
    DebtorBasicInfo,
    DebtorPhone,
    DebtorEmail,
    DebtorAddress,
    DebtorWechat
)


class DebtorDataGenerator:
    """债务人测试数据生成器"""
    
    def __init__(self):
        # 中文姓氏库
        self.surnames = [
            '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
            '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
            '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧'
        ]
        
        # 中文名字库
        self.given_names = [
            '伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军',
            '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞',
            '平', '刚', '桂英', '建华', '文', '华', '金凤', '素英', '建国', '德华'
        ]
        
        # 公司名称后缀
        self.company_suffixes = [
            '有限公司', '股份有限公司', '科技有限公司', '贸易有限公司',
            '实业有限公司', '投资有限公司', '建设有限公司', '工程有限公司'
        ]
        
        # 公司名称前缀
        self.company_prefixes = [
            '华润', '中信', '恒大', '万科', '碧桂园', '保利', '招商', '中海',
            '绿地', '融创', '新城', '龙湖', '世茂', '金地', '远洋', '华夏',
            '泰禾', '富力', '雅居乐', '中南', '正荣', '阳光城', '金科', '荣盛'
        ]
        
        # 地址组件
        self.provinces = ['北京市', '上海市', '广东省', '江苏省', '浙江省', '山东省', '河南省', '四川省']
        self.cities = ['海淀区', '朝阳区', '浦东新区', '黄浦区', '天河区', '越秀区', '玄武区', '鼓楼区']
        self.streets = ['中山路', '人民路', '建设路', '解放路', '和平路', '胜利路', '光明路', '幸福路']
        
    def generate_phone(self):
        """生成手机号码"""
        prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                   '150', '151', '152', '153', '155', '156', '157', '158', '159',
                   '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
        prefix = random.choice(prefixes)
        suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        return prefix + suffix
    
    def generate_id_card(self):
        """生成身份证号码（18位）"""
        # 地区码（前6位）
        area_codes = ['110101', '310101', '440101', '320101', '330101', '370101']
        area_code = random.choice(area_codes)
        
        # 出生日期（8位）
        year = random.randint(1970, 2000)
        month = random.randint(1, 12)
        day = random.randint(1, 28)
        birth_date = f"{year:04d}{month:02d}{day:02d}"
        
        # 顺序码（3位）
        sequence = f"{random.randint(1, 999):03d}"
        
        # 前17位
        id_17 = area_code + birth_date + sequence
        
        # 计算校验码
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        sum_val = sum(int(id_17[i]) * weights[i] for i in range(17))
        check_code = check_codes[sum_val % 11]
        
        return id_17 + check_code
    
    def generate_business_license(self):
        """生成营业执照号码（18位统一社会信用代码）"""
        # 登记管理部门代码（1位）
        dept_code = random.choice(['9', '5', '1'])
        
        # 机构类别代码（1位）
        org_code = random.choice(['1', '2', '3'])
        
        # 登记管理机关行政区划码（6位）
        area_codes = ['110000', '310000', '440000', '320000', '330000', '370000']
        area_code = random.choice(area_codes)
        
        # 主体标识码（9位）
        main_code = ''.join([str(random.randint(0, 9)) for _ in range(9)])
        
        # 校验码（1位）
        check_code = str(random.randint(0, 9))
        
        return dept_code + org_code + area_code + main_code + check_code
    
    def generate_passport(self):
        """生成护照号码"""
        # 护照号码格式：1个字母 + 8位数字
        letter = random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ')
        numbers = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        return letter + numbers
    
    def generate_email(self, name):
        """生成邮箱地址"""
        domains = ['qq.com', '163.com', '126.com', 'gmail.com', 'sina.com', 'sohu.com']
        domain = random.choice(domains)
        # 使用拼音或随机数字作为邮箱前缀
        prefix = f"{name.lower()}{random.randint(100, 999)}"
        return f"{prefix}@{domain}"
    
    def generate_wechat(self, name):
        """生成微信号"""
        # 微信号格式：字母开头，6-20位字母数字下划线
        prefix = name.lower()
        suffix = str(random.randint(100, 9999))
        return f"{prefix}_{suffix}"
    
    def generate_address(self):
        """生成地址"""
        province = random.choice(self.provinces)
        city = random.choice(self.cities)
        street = random.choice(self.streets)
        number = random.randint(1, 999)
        unit = random.randint(1, 50)
        room = random.randint(101, 2999)
        return f"{province}{city}{street}{number}号{unit}单元{room}室"
    
    def generate_individual_name(self):
        """生成个人姓名"""
        surname = random.choice(self.surnames)
        given_name = random.choice(self.given_names)
        if random.random() < 0.3:  # 30%概率生成双字名
            given_name += random.choice(self.given_names)
        return surname + given_name
    
    def generate_company_name(self):
        """生成公司名称"""
        prefix = random.choice(self.company_prefixes)
        suffix = random.choice(self.company_suffixes)
        # 添加一些随机的业务描述词
        business_words = ['科技', '实业', '贸易', '投资', '建设', '工程', '咨询', '服务']
        business = random.choice(business_words)
        return f"{prefix}{business}{suffix}"
    
    def create_debtor_contact_info(self, debtor_name):
        """创建债务人联系方式信息"""
        contacts = {
            'phones': [],
            'emails': [],
            'addresses': [],
            'wechats': []
        }
        
        # 每个债务人至少有1个联系方式，最多3个
        phone_count = random.randint(1, 2)
        email_count = random.randint(0, 1)
        address_count = random.randint(1, 1)
        wechat_count = random.randint(0, 1)
        
        # 创建电话
        for i in range(phone_count):
            phone = DebtorPhone.objects.create(
                phone=self.generate_phone(),
                phone_type=random.choice(['手机', '座机', '工作电话']),
                is_primary=(i == 0)  # 第一个设为主要联系方式
            )
            contacts['phones'].append(phone)
        
        # 创建邮箱
        for i in range(email_count):
            email = DebtorEmail.objects.create(
                email=self.generate_email(debtor_name),
                email_type=random.choice(['个人邮箱', '工作邮箱']),
                is_primary=(i == 0)
            )
            contacts['emails'].append(email)
        
        # 创建地址
        for i in range(address_count):
            address = DebtorAddress.objects.create(
                address=self.generate_address(),
                address_type=random.choice(['居住地址', '工作地址', '通讯地址']),
                is_primary=(i == 0)
            )
            contacts['addresses'].append(address)
        
        # 创建微信
        for i in range(wechat_count):
            wechat = DebtorWechat.objects.create(
                wechat=self.generate_wechat(debtor_name),
                wechat_type=random.choice(['个人微信', '工作微信']),
                is_primary=(i == 0)
            )
            contacts['wechats'].append(wechat)
        
        return contacts
    
    def create_debtor_with_contacts(self):
        """创建债务人及其联系方式"""
        # 随机选择债务人类型
        debtor_type = random.choice([choice[0] for choice in DebtorBasicInfo.DEBTOR_TYPE_CHOICES])

        # 随机选择证件类型
        id_type = random.choice([choice[0] for choice in DebtorBasicInfo.ID_TYPE_CHOICES])

        # 根据类型生成相应的名称和证件信息
        if debtor_type == 'individual':
            debtor_name = self.generate_individual_name()
            if id_type == 'business_license':  # 个人不应该有营业执照，重新选择
                id_type = random.choice(['id_card', 'passport'])
        elif debtor_type == 'legal_person':
            debtor_name = self.generate_company_name()
            if id_type in ['id_card', 'passport']:  # 法人应该有营业执照
                id_type = 'business_license'
        else:  # other
            debtor_name = random.choice([self.generate_individual_name(), self.generate_company_name()])

        # 根据证件类型生成证件号码
        if id_type == 'id_card':
            id_number = self.generate_id_card()
        elif id_type == 'passport':
            id_number = self.generate_passport()
        else:  # business_license
            id_number = self.generate_business_license()

        # 生成债务人基本信息
        debtor_data = {
            'debtor_type': debtor_type,
            'debtor_name': debtor_name,
            'id_type': id_type,
            'id_number': id_number,
            'created_by': 1,  # 假设系统用户ID为1
            'updated_by': 1
        }

        # 创建债务人记录
        debtor = DebtorBasicInfo.objects.create(**debtor_data)

        # 创建并关联联系方式
        contacts = self.create_debtor_contact_info(debtor_name)

        # 关联联系方式到债务人
        if contacts['phones']:
            debtor.phones.set(contacts['phones'])
        if contacts['emails']:
            debtor.emails.set(contacts['emails'])
        if contacts['addresses']:
            debtor.addresses.set(contacts['addresses'])
        if contacts['wechats']:
            debtor.wechats.set(contacts['wechats'])

        return debtor

    def generate_test_data(self, count=20):
        """生成指定数量的测试数据"""
        print(f"开始生成 {count} 条债务人测试数据...")

        created_count = 0

        try:
            with transaction.atomic():  # 使用数据库事务确保数据一致性
                for i in range(count):
                    try:
                        debtor = self.create_debtor_with_contacts()
                        created_count += 1
                        print(f"✓ 已创建第 {created_count} 条记录: {debtor.debtor_name}")
                    except Exception as e:
                        print(f"✗ 创建第 {i+1} 条记录时出错: {str(e)}")
                        continue

                print(f"\n🎉 数据生成完成！成功创建 {created_count} 条债务人记录")

        except Exception as e:
            print(f"❌ 数据生成过程中发生错误: {str(e)}")
            return False

        return True

    def clean_existing_data(self):
        """清理现有测试数据（可选）"""
        print("正在清理现有债务人测试数据...")

        try:
            with transaction.atomic():
                # 删除所有债务人记录（会级联删除关联的联系方式）
                deleted_count = DebtorBasicInfo.objects.all().count()
                DebtorBasicInfo.objects.all().delete()

                # 清理孤立的联系方式记录
                DebtorPhone.objects.filter(debtorbasicinfo__isnull=True).delete()
                DebtorEmail.objects.filter(debtorbasicinfo__isnull=True).delete()
                DebtorAddress.objects.filter(debtorbasicinfo__isnull=True).delete()
                DebtorWechat.objects.filter(debtorbasicinfo__isnull=True).delete()

                print(f"✓ 已清理 {deleted_count} 条现有记录")

        except Exception as e:
            print(f"❌ 清理数据时发生错误: {str(e)}")
            return False

        return True


def main():
    """主函数"""
    print("=" * 60)
    print("债务人基本信息测试数据生成脚本")
    print("=" * 60)

    generator = DebtorDataGenerator()

    # 询问是否清理现有数据
    clean_choice = input("是否清理现有债务人数据？(y/N): ").lower().strip()
    if clean_choice in ['y', 'yes']:
        if not generator.clean_existing_data():
            return

    # 询问生成数据数量
    try:
        count = input("请输入要生成的数据条数 (默认20): ").strip()
        count = int(count) if count else 20
        if count <= 0:
            print("❌ 数据条数必须大于0")
            return
    except ValueError:
        print("❌ 请输入有效的数字")
        return

    # 生成测试数据
    success = generator.generate_test_data(count)

    if success:
        print("\n✅ 脚本执行完成！")
    else:
        print("\n❌ 脚本执行失败！")


if __name__ == '__main__':
    main()
