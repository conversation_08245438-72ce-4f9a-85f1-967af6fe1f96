#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音外呼记录测试数据生成脚本
执行方式：python generate_voice_call_records.py [--count 数量] [--clean]
"""

import os
import sys
import django
import random
import argparse
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction

# 设置Django环境 - 修正路径为项目根目录
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ops_management.settings')
django.setup()

from apps.outbound_communication.models import VoiceCallRecord
from apps.counterparty.models import CreditorBasicInfo, DebtorBasicInfo


class VoiceCallRecordDataGenerator:
    """语音外呼记录数据生成器"""
    
    def __init__(self):
        """初始化数据生成器"""
        # 手机号前缀列表 - 中国大陆常用手机号段
        self.mobile_prefixes = [
            '130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
            '150', '151', '152', '153', '155', '156', '157', '158', '159',
            '180', '181', '182', '183', '184', '185', '186', '187', '188', '189',
            '170', '171', '172', '173', '174', '175', '176', '177', '178'
        ]
        
        # 固话区号列表 - 主要城市区号
        self.landline_area_codes = [
            '010', '020', '021', '022', '023', '024', '025', '027', '028', '029',
            '0311', '0351', '0371', '0431', '0451', '0531', '0551', '0571', '0591', '0731'
        ]
        
        # 主叫号码池 - 外呼系统使用的号码
        self.caller_numbers = [
            '02088888888', '02166666666', '01055555555', '02277777777',
            '95588', '95533', '95599', '************', '************'
        ]
        
        # 外呼结果备注模板
        self.call_result_templates = {
            'connected': [
                '客户已确认还款计划，约定本月底前完成首期还款',
                '客户表示理解债务情况，同意协商还款方案',
                '客户确认收到催收通知，承诺尽快处理欠款',
                '与客户详细沟通债务详情，客户同意分期还款',
                '客户已了解法律后果，承诺按时履行还款义务',
                '通话顺畅，客户配合度较高，已记录联系方式',
                '客户确认个人信息无误，同意后续跟进联系'
            ],
            'not_connected': [
                '号码无人接听，建议更换时间段重新外呼',
                '电话未接通，可能客户正在忙碌中',
                '多次拨打均未接通，建议核实号码有效性'
            ],
            'busy': [
                '客户电话忙线，建议稍后重新拨打',
                '线路繁忙，未能接通客户电话'
            ],
            'power_off': [
                '客户手机关机，无法联系',
                '电话显示关机状态，建议更换联系方式'
            ],
            'no_answer': [
                '电话响铃但无人接听，建议更换时间重试',
                '客户可能不在身边，电话无人应答'
            ],
            'invalid_number': [
                '号码无效或已停机，需要更新客户联系方式',
                '电话号码错误，无法接通'
            ],
            'rejected': [
                '客户主动拒接电话，可能对催收有抵触情绪',
                '电话被客户挂断，建议采用其他联系方式'
            ],
            'failed': [
                '系统故障导致呼叫失败，需要重新发起外呼',
                '网络异常，外呼任务执行失败'
            ]
        }
    
    def generate_phone_number(self, number_type='mobile'):
        """
        生成随机电话号码
        
        Args:
            number_type (str): 号码类型，'mobile'为手机号，'landline'为固话
            
        Returns:
            str: 生成的电话号码
        """
        if number_type == 'mobile':
            # 生成手机号：前缀 + 8位随机数字
            prefix = random.choice(self.mobile_prefixes)
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            return prefix + suffix
        else:
            # 生成固话：区号 + 7-8位随机数字
            area_code = random.choice(self.landline_area_codes)
            number_length = random.choice([7, 8])
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(number_length)])
            return area_code + suffix
    
    def generate_task_batch_id(self):
        """
        生成外呼任务批次号
        
        Returns:
            str: 任务批次号，格式：BATCH_YYYYMMDD_XXX
        """
        # 随机选择日期（最近30天内）
        base_date = timezone.now().date()
        random_days = random.randint(0, 30)
        batch_date = base_date - timedelta(days=random_days)
        
        # 生成批次序号（001-999）
        batch_seq = random.randint(1, 999)
        
        return f"BATCH_{batch_date.strftime('%Y%m%d')}_{batch_seq:03d}"
    
    def generate_call_times(self, call_status):
        """
        根据呼叫状态生成合理的时间数据
        
        Args:
            call_status (str): 呼叫状态
            
        Returns:
            tuple: (call_start_time, call_end_time, call_duration)
        """
        # 生成呼叫开始时间（最近30天内的工作时间）
        base_time = timezone.now()
        random_days = random.randint(0, 30)
        random_hours = random.randint(9, 18)  # 工作时间9-18点
        random_minutes = random.randint(0, 59)
        random_seconds = random.randint(0, 59)
        
        call_start_time = base_time.replace(
            hour=random_hours, 
            minute=random_minutes, 
            second=random_seconds, 
            microsecond=0
        ) - timedelta(days=random_days)
        
        if call_status == 'connected':
            # 接通状态：生成通话时长（30秒-10分钟）
            call_duration = random.randint(30, 600)
            call_end_time = call_start_time + timedelta(seconds=call_duration)
        elif call_status in ['busy', 'no_answer', 'rejected']:
            # 这些状态可能有短暂的响铃时间
            call_duration = 0
            ring_duration = random.randint(5, 30)
            call_end_time = call_start_time + timedelta(seconds=ring_duration)
        else:
            # 其他状态：无通话时长
            call_duration = 0
            call_end_time = None
        
        return call_start_time, call_end_time, call_duration
    
    def generate_recording_file_path(self, call_status, call_start_time):
        """
        生成录音文件路径
        
        Args:
            call_status (str): 呼叫状态
            call_start_time (datetime): 呼叫开始时间
            
        Returns:
            str: 录音文件路径，未接通时返回None
        """
        if call_status != 'connected':
            return None
        
        # 生成录音文件路径：/recordings/YYYY/MM/DD/call_timestamp.wav
        date_path = call_start_time.strftime('%Y/%m/%d')
        timestamp = call_start_time.strftime('%Y%m%d_%H%M%S')
        file_name = f"call_{timestamp}_{random.randint(1000, 9999)}.wav"
        
        return f"/recordings/{date_path}/{file_name}"
    
    def get_random_creditor_debtor(self):
        """
        随机获取债权人和债务人
        
        Returns:
            tuple: (creditor, debtor) 可能为None
        """
        # 获取现有的债权人和债务人
        creditors = list(CreditorBasicInfo.objects.all())
        debtors = list(DebtorBasicInfo.objects.all())
        
        # 80%概率关联债权人，70%概率关联债务人
        creditor = random.choice(creditors) if creditors and random.random() < 0.8 else None
        debtor = random.choice(debtors) if debtors and random.random() < 0.7 else None
        
        return creditor, debtor
    
    def create_voice_call_record(self):
        """
        创建单条语音外呼记录
        
        Returns:
            VoiceCallRecord: 创建的语音外呼记录对象
        """
        # 随机选择呼叫状态
        call_status = random.choice([choice[0] for choice in VoiceCallRecord.CALL_STATUS_CHOICES])
        
        # 生成电话号码
        number_type = random.choice(['mobile', 'landline'])
        called_number = self.generate_phone_number(number_type)
        caller_number = random.choice(self.caller_numbers)
        
        # 生成时间数据
        call_start_time, call_end_time, call_duration = self.generate_call_times(call_status)
        
        # 生成录音文件路径
        recording_file_path = self.generate_recording_file_path(call_status, call_start_time)
        
        # 生成任务批次号（60%概率有批次号）
        task_batch_id = self.generate_task_batch_id() if random.random() < 0.6 else None
        
        # 获取关联的债权人和债务人
        creditor, debtor = self.get_random_creditor_debtor()
        
        # 生成外呼结果备注
        result_templates = self.call_result_templates.get(call_status, ['外呼完成'])
        call_result_notes = random.choice(result_templates) if random.random() < 0.8 else None
        
        # 创建语音外呼记录
        voice_call_record = VoiceCallRecord(
            called_number=called_number,
            caller_number=caller_number,
            call_status=call_status,
            call_duration=call_duration,
            call_start_time=call_start_time,
            call_end_time=call_end_time,
            recording_file_path=recording_file_path,
            task_batch_id=task_batch_id,
            creditor=creditor,
            debtor=debtor,
            call_result_notes=call_result_notes,
            created_by=1,  # 假设系统用户ID为1
            updated_by=1
        )
        
        return voice_call_record
    
    def generate_test_data(self, count=20, clean_existing=False):
        """
        生成指定数量的测试数据
        
        Args:
            count (int): 生成数据数量
            clean_existing (bool): 是否清理现有测试数据
        """
        print(f"开始生成 {count} 条语音外呼记录测试数据...")
        
        if clean_existing:
            print("正在清理现有测试数据...")
            # 可以根据需要添加清理逻辑，比如删除包含特定标识的测试数据
            # VoiceCallRecord.objects.filter(call_result_notes__contains='测试数据').delete()
        
        created_count = 0
        voice_call_records = []
        
        try:
            with transaction.atomic():  # 使用数据库事务确保数据一致性
                for i in range(count):
                    try:
                        voice_call_record = self.create_voice_call_record()
                        voice_call_records.append(voice_call_record)
                        created_count += 1
                        
                        # 显示进度
                        status_display = voice_call_record.get_call_status_display()
                        print(f"✓ 已生成第 {created_count} 条记录: {voice_call_record.caller_number} -> {voice_call_record.called_number} ({status_display})")
                        
                    except Exception as e:
                        print(f"✗ 生成第 {i+1} 条记录时出错: {str(e)}")
                        continue
                
                # 批量创建数据
                VoiceCallRecord.objects.bulk_create(voice_call_records)
                print(f"\n🎉 数据生成完成！成功创建 {created_count} 条语音外呼记录")
                
                # 显示统计信息
                self.show_statistics()
                
        except Exception as e:
            print(f"❌ 数据生成过程中发生错误: {str(e)}")
            return False
        
        return True
    
    def show_statistics(self):
        """显示数据统计信息"""
        total_count = VoiceCallRecord.objects.count()
        print(f"\n📊 数据库统计信息:")
        print(f"语音外呼记录总数: {total_count}")
        
        # 按呼叫状态统计
        for status_code, status_name in VoiceCallRecord.CALL_STATUS_CHOICES:
            count = VoiceCallRecord.objects.filter(call_status=status_code).count()
            print(f"{status_name}: {count} 条")


def main():
    """主函数 - 处理命令行参数并执行数据生成"""
    parser = argparse.ArgumentParser(description='生成语音外呼记录测试数据')
    parser.add_argument('--count', type=int, default=20, help='生成数据数量（默认20条）')
    parser.add_argument('--clean', action='store_true', help='清理现有测试数据')
    
    args = parser.parse_args()
    
    # 创建数据生成器并执行
    generator = VoiceCallRecordDataGenerator()
    success = generator.generate_test_data(count=args.count, clean_existing=args.clean)
    
    if success:
        print("\n✅ 语音外呼记录测试数据生成完成！")
    else:
        print("\n❌ 数据生成失败，请检查错误信息")
        sys.exit(1)


if __name__ == '__main__':
    main()
