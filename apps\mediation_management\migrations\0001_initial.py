# Generated by Django 4.1.13 on 2025-07-22 12:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('counterparty', '0001_initial'),
        ('user', '0001_initial'),
        ('data_governance', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MediationCase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('case_number', models.CharField(help_text='格式：GZTJ+YYYYMMDD+0001', max_length=50, unique=True, verbose_name='调解案件号')),
                ('case_status', models.CharField(choices=[('draft', '待发起'), ('initiated', '已发起'), ('pending_confirm', '待确认'), ('in_progress', '进行中'), ('completed', '已完成'), ('closed', '已关闭')], default='draft', help_text='案件的流程处理状态', max_length=20, verbose_name='案件状态')),
                ('mediation_config', models.JSONField(help_text='JSON格式的调解配置信息', verbose_name='调解信息配置')),
                ('asset_package_row_number', models.IntegerField(blank=True, help_text='案件在资产包Excel文件中对应的数据行号', null=True, verbose_name='资产包应用行号')),
                ('initiate_date', models.DateTimeField(blank=True, help_text='案件正式发起的时间', null=True, verbose_name='发起日期')),
                ('close_date', models.DateTimeField(blank=True, help_text='案件结束关闭的时间', null=True, verbose_name='关闭日期')),
                ('mediation_agreement', models.FileField(blank=True, help_text='调解达成的协议PDF文件', null=True, upload_to='./upload', verbose_name='调解协议')),
                ('electronic_signature', models.ImageField(blank=True, help_text='当事人的电子签名图片', null=True, upload_to='./upload', verbose_name='电子签名')),
                ('notarization_status', models.CharField(choices=[('not_notarized', '未公证'), ('notarized', '已公证')], default='not_notarized', help_text='调解协议的公证处理状态', max_length=20, verbose_name='协议公证状态')),
                ('asset_package', models.ForeignKey(blank=True, help_text='案件关联的资产包信息', null=True, on_delete=django.db.models.deletion.SET_NULL, to='data_governance.assetpackagemanagement', verbose_name='资产包')),
            ],
            options={
                'verbose_name': '调解案件',
                'verbose_name_plural': '调解案件',
                'db_table': 't_mediation_case',
            },
        ),
        migrations.CreateModel(
            name='MediationCaseFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('file', models.FileField(help_text='案件相关的附件文件', upload_to='./upload', verbose_name='附件文件')),
                ('file_name', models.CharField(help_text='上传文件的原始名称', max_length=255, verbose_name='附件原名')),
            ],
            options={
                'verbose_name': '调解案件附件',
                'verbose_name_plural': '调解案件附件',
                'db_table': 't_mediation_case_file',
            },
        ),
        migrations.CreateModel(
            name='MediationPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('plan_name', models.CharField(help_text='调解方案的业务名称标识', max_length=200, verbose_name='方案名称')),
                ('plan_status', models.CharField(choices=[('inactive', '未生效'), ('active', '已生效')], default='inactive', help_text='方案的执行生效状态', max_length=20, verbose_name='方案状态')),
                ('approval_status', models.CharField(choices=[('pending', '待审批'), ('approved', '已通过'), ('rejected', '未通过')], default='pending', help_text='方案的审批流程状态', max_length=20, verbose_name='审批状态')),
                ('plan_config', models.JSONField(help_text='JSON格式的方案配置信息', verbose_name='方案配置')),
                ('approval_comment', models.TextField(blank=True, help_text='审批过程中的意见、说明或备注信息', null=True, verbose_name='审批意见')),
                ('asset_package', models.ForeignKey(blank=True, help_text='该调解方案关联的资产包', null=True, on_delete=django.db.models.deletion.SET_NULL, to='data_governance.assetpackagemanagement', verbose_name='资产包')),
                ('mediation_case', models.ForeignKey(blank=True, help_text='该方案关联的调解案件', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mediation_management.mediationcase', verbose_name='调解案件')),
            ],
            options={
                'verbose_name': '调解方案',
                'verbose_name_plural': '调解方案',
                'db_table': 't_mediation_plan',
            },
        ),
        migrations.AddField(
            model_name='mediationcase',
            name='attachments',
            field=models.ManyToManyField(blank=True, help_text='案件相关的附件文件', to='mediation_management.mediationcasefile', verbose_name='案件附件'),
        ),
        migrations.AddField(
            model_name='mediationcase',
            name='creditor',
            field=models.ForeignKey(blank=True, help_text='案件的债权方当事人', null=True, on_delete=django.db.models.deletion.SET_NULL, to='counterparty.creditorbasicinfo', verbose_name='债权人'),
        ),
        migrations.AddField(
            model_name='mediationcase',
            name='debtor',
            field=models.ForeignKey(blank=True, help_text='案件的债务方当事人', null=True, on_delete=django.db.models.deletion.SET_NULL, to='counterparty.debtorbasicinfo', verbose_name='债务人'),
        ),
        migrations.AddField(
            model_name='mediationcase',
            name='mediation_plan',
            field=models.ForeignKey(blank=True, help_text='案件采用的调解方案', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mediation_management.mediationplan', verbose_name='确认的调解方案'),
        ),
        migrations.AddField(
            model_name='mediationcase',
            name='mediator',
            field=models.ForeignKey(blank=True, help_text='负责案件调解的工作人员', null=True, on_delete=django.db.models.deletion.SET_NULL, to='user.systemuser', verbose_name='调解员'),
        ),
    ]
