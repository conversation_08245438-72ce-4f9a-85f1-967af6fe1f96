#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : urls.py
<AUTHOR> JT_DA
@Date     : 2025/07/14
@File_Desc: 对手方URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from apps.counterparty.views.creditor_basic_info_view_set import CreditorBasicInfoViewSet
from apps.counterparty.views.debtor_basic_info_view_set import DebtorBasicInfoViewSet

# 创建路由器
router = DefaultRouter()

# 注册ViewSet
router.register(r"creditor_basic_info", CreditorBasicInfoViewSet, basename="creditor_basic_info")
router.register(r"debtor_basic_info", DebtorBasicInfoViewSet, basename="debtor_basic_info")

urlpatterns = [
    # 对手方相关接口
    path("", include(router.urls)),
]
