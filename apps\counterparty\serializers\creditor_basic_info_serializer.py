#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : creditor_basic_info_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/07/14
@File_Desc: 债权人基本信息序列化器
"""

from rest_framework import serializers
from apps.counterparty.models import CreditorBasicInfo, CreditorPhone, CreditorEmail, CreditorAddress
from utils.validator_helper import validate_social_credit_code
from id_validator import validator


class CreditorPhoneSerializer(serializers.ModelSerializer):
    """债权人联系电话序列化器"""
    
    class Meta:
        model = CreditorPhone
        exclude = ["revision", "created_by", "created_time", "updated_by", "updated_time"]


class CreditorEmailSerializer(serializers.ModelSerializer):
    """债权人邮箱序列化器"""
    
    class Meta:
        model = CreditorEmail
        exclude = ["revision", "created_by", "created_time", "updated_by", "updated_time"]


class CreditorAddressSerializer(serializers.ModelSerializer):
    """债权人地址序列化器"""
    
    class Meta:
        model = CreditorAddress
        exclude = ["revision", "created_by", "created_time", "updated_by", "updated_time"]


class CreditorBasicInfoSerializer(serializers.ModelSerializer):
    """债权人基本信息序列化器"""

    creditor_type_cn = serializers.CharField(source="get_creditor_type_display", read_only=True)
    id_type_cn = serializers.CharField(source="get_id_type_display", read_only=True)
    
    # 嵌套序列化器显示关联信息
    phones = CreditorPhoneSerializer(many=True, read_only=True)
    emails = CreditorEmailSerializer(many=True, read_only=True)
    addresses = CreditorAddressSerializer(many=True, read_only=True)

    class Meta:
        model = CreditorBasicInfo
        exclude = ["revision", "created_by", "created_time", "updated_by", "updated_time"]
        read_only_fields = [
            "id",
            "creditor_type_cn",
            "id_type_cn",
            "phones",
            "emails",
            "addresses",
        ]

    def validate_id_number(self, value):
        """验证证件号码格式"""
        import re
        
        if not value:
            return value
            
        # 简单的证件号码格式验证
        if len(value) < 6:
            raise serializers.ValidationError("证件号码长度不能少于6位")
            
        return value

    def validate_creditor_name(self, value):
        """验证债权人名称"""
        if value and len(value.strip()) < 2:
            raise serializers.ValidationError("债权人名称长度不能少于2个字符")
        return value.strip() if value else value


class CreditorBasicInfoCreateSerializer(serializers.ModelSerializer):
    """创建债权人基本信息序列化器"""
    
    # 支持ManyToManyField字段的嵌套创建
    phones = CreditorPhoneSerializer(many=True, required=False)
    emails = CreditorEmailSerializer(many=True, required=False)
    addresses = CreditorAddressSerializer(many=True, required=False)

    class Meta:
        model = CreditorBasicInfo
        fields = ["creditor_type", "creditor_name", "id_type", "id_number", "phones", "emails", "addresses"]

    def validate_id_number(self, value):
        """验证证件号码格式"""
        import re
        
        if not value:
            return value
            
        # 简单的证件号码格式验证
        if len(value) < 6:
            raise serializers.ValidationError("证件号码长度不能少于6位")
            
        return value

    def validate_creditor_name(self, value):
        """验证债权人名称"""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("债权人名称不能为空且长度不能少于2个字符")
        return value.strip()

    def validate(self, data):
        """验证证件类型与证件号码的匹配"""
        id_type = data.get("id_type")
        id_number = data.get("id_number")
        
        if id_type and id_number:
            if id_type == "id_card":
                # 使用id-validator库验证身份证号码
                if not validator.is_valid(id_number):
                    raise serializers.ValidationError("身份证号码格式不正确")
            elif id_type == "business_license":
                # 使用自定义函数验证营业执照统一社会信用代码
                if not validate_social_credit_code(id_number):
                    raise serializers.ValidationError("营业执照统一社会信用代码格式不正确")
            
        return data

    def create(self, validated_data):
        """创建债权人信息及其关联数据"""
        # 提取ManyToManyField数据
        phones_data = validated_data.pop('phones', [])
        emails_data = validated_data.pop('emails', [])
        addresses_data = validated_data.pop('addresses', [])
        
        # 创建债权人基本信息
        creditor = CreditorBasicInfo.objects.create(**validated_data)
        
        # 创建并关联电话信息
        for phone_data in phones_data:
            phone = CreditorPhone.objects.create(**phone_data)
            creditor.phones.add(phone)
            
        # 创建并关联邮箱信息
        for email_data in emails_data:
            email = CreditorEmail.objects.create(**email_data)
            creditor.emails.add(email)
            
        # 创建并关联地址信息
        for address_data in addresses_data:
            address = CreditorAddress.objects.create(**address_data)
            creditor.addresses.add(address)
            
        return creditor


class CreditorBasicInfoUpdateSerializer(serializers.ModelSerializer):
    """更新债权人基本信息序列化器"""
    
    # 支持ManyToManyField字段的嵌套更新
    phones = CreditorPhoneSerializer(many=True, required=False)
    emails = CreditorEmailSerializer(many=True, required=False)
    addresses = CreditorAddressSerializer(many=True, required=False)

    class Meta:
        model = CreditorBasicInfo
        fields = ["creditor_type", "creditor_name", "id_type", "id_number", "phones", "emails", "addresses"]

    def validate_id_number(self, value):
        """验证证件号码格式"""
        if not value:
            return value
            
        # 简单的证件号码格式验证
        if len(value) < 6:
            raise serializers.ValidationError("证件号码长度不能少于6位")
            
        return value

    def validate_creditor_name(self, value):
        """验证债权人名称"""
        if value and len(value.strip()) < 2:
            raise serializers.ValidationError("债权人名称长度不能少于2个字符")
        return value.strip() if value else value

    def validate(self, data):
        """验证证件类型与证件号码的匹配"""
        id_type = data.get("id_type")
        id_number = data.get("id_number")
        
        if id_type and id_number:
            if id_type == "id_card":
                # 使用id-validator库验证身份证号码
                if not validator.is_valid(id_number):
                    raise serializers.ValidationError("身份证号码格式不正确")
            elif id_type == "business_license":
                # 使用自定义函数验证营业执照统一社会信用代码
                if not validate_social_credit_code(id_number):
                    raise serializers.ValidationError("营业执照统一社会信用代码格式不正确")
            
        return data

    def update(self, instance, validated_data):
        """更新债权人信息及其关联数据"""
        # 提取ManyToManyField数据
        phones_data = validated_data.pop('phones', None)
        emails_data = validated_data.pop('emails', None)
        addresses_data = validated_data.pop('addresses', None)
        
        # 更新基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 更新电话信息
        if phones_data is not None:
            instance.phones.clear()
            for phone_data in phones_data:
                phone = CreditorPhone.objects.create(**phone_data)
                instance.phones.add(phone)
                
        # 更新邮箱信息
        if emails_data is not None:
            instance.emails.clear()
            for email_data in emails_data:
                email = CreditorEmail.objects.create(**email_data)
                instance.emails.add(email)
                
        # 更新地址信息
        if addresses_data is not None:
            instance.addresses.clear()
            for address_data in addresses_data:
                address = CreditorAddress.objects.create(**address_data)
                instance.addresses.add(address)
                
        return instance 