#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : serializers.py
<AUTHOR> JT_DA
@Date     : 2025/07/10
@File_Desc: 投诉建议序列化器
"""

from rest_framework import serializers
from apps.feedback.models import Feedback


class FeedbackSerializer(serializers.ModelSerializer):
    """投诉建议序列化器"""

    feedback_type_cn = serializers.CharField(source="get_feedback_type_display", read_only=True)
    category_cn = serializers.CharField(source="get_category_display", read_only=True)
    process_status_cn = serializers.CharField(source="get_process_status_display", read_only=True)

    # 动态提供类别选项
    # suggestion_categories = serializers.SerializerMethodField()
    # complaint_categories = serializers.SerializerMethodField()

    class Meta:
        model = Feedback
        exclude = ["revision", "created_by", "created_time", "updated_by", "updated_time"]
        read_only_fields = [
            "id",
            "feedback_type_cn",
            "category_cn",
            "process_status_cn",
            "suggestion_categories",
            "complaint_categories",
        ]

    # def get_suggestion_categories(self, obj):
    #     """获取意见建议类别选项"""
    #     return dict(Feedback.SUGGESTION_CATEGORY_CHOICES)

    # def get_complaint_categories(self, obj):
    #     """获取服务投诉类别选项"""
    #     return dict(Feedback.COMPLAINT_CATEGORY_CHOICES)

    def validate_phone_number(self, value):
        """验证手机号码格式"""
        import re

        if value and not re.match(r"^1[3-9]\d{9}$", value):
            raise serializers.ValidationError("请输入正确的手机号码格式")
        return value


class FeedbackCreateSerializer(serializers.ModelSerializer):
    """创建投诉建议序列化器"""

    class Meta:
        model = Feedback
        fields = ["feedback_type", "case_number", "category", "description", "phone_number"]

    def validate_phone_number(self, value):
        """验证手机号码格式"""
        import re

        if value and not re.match(r"^1[3-9]\d{9}$", value):
            raise serializers.ValidationError("请输入正确的手机号码格式")
        return value

    def validate(self, data):
        """验证类别与反馈类型是否匹配"""
        feedback_type = data.get("feedback_type")
        category = data.get("category")

        suggestion_categories = [choice[0] for choice in Feedback.SUGGESTION_CATEGORY_CHOICES]
        complaint_categories = [choice[0] for choice in Feedback.COMPLAINT_CATEGORY_CHOICES]

        if feedback_type == "suggestion" and category not in suggestion_categories:
            raise serializers.ValidationError("意见建议类型的类别选择不正确")
        elif feedback_type == "complaint" and category not in complaint_categories:
            raise serializers.ValidationError("服务投诉类型的类别选择不正确")

        return data


class FeedbackHandleSerializer(serializers.ModelSerializer):
    """处理投诉建议序列化器"""

    class Meta:
        model = Feedback
        fields = ["process_status", "handler", "handle_time", "handle_result"]

    def validate_handler(self, value):
        """验证处理人字段"""
        if value and len(value.strip()) < 2:
            raise serializers.ValidationError("处理人姓名长度不能少于2个字符")
        return value.strip() if value else value
