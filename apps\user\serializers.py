#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : serializers.py
<AUTHOR> JT_DA
@Date     : 2025/07/21
@File_Desc: 用户模块序列化器
"""

from rest_framework import serializers
from apps.user.models import SystemUser
import re


class SystemUserSerializer(serializers.ModelSerializer):
    """系统用户序列化器"""

    class Meta:
        model = SystemUser
        fields = [
            "id",
            "username",
            "role_id",
            "role_name",
            "group_id",
            "group_name",
            "phone_number",
            "email",
            "is_active",
        ]
        read_only_fields = [
            "id",
            "username",
            "role_id",
            "role_name",
            "group_id",
            "group_name",
            "phone_number",
            "email",
            "is_active",
        ]


class ChangePasswordSerializer(serializers.Serializer):
    """用户密码修改序列化器

    用于验证和处理用户密码修改请求的输入数据，包含密码强度验证和逻辑验证。
    确保新密码符合安全要求，验证新旧密码不能相同，并确保新密码确认输入一致。
    """

    old_password = serializers.CharField(
        required=True,
        min_length=6,
        max_length=128,
        write_only=True,
        help_text="旧密码，用于验证用户身份",
        error_messages={
            'required': '旧密码不能为空',
            'min_length': '旧密码长度不能少于6位',
            'max_length': '旧密码长度不能超过128位'
        }
    )

    new_password = serializers.CharField(
        required=True,
        min_length=6,
        max_length=128,
        write_only=True,
        help_text="新密码，用户希望设置的新密码",
        error_messages={
            'required': '新密码不能为空',
            'min_length': '新密码长度不能少于6位',
            'max_length': '新密码长度不能超过128位'
        }
    )

    password = serializers.CharField(
        required=True,
        min_length=6,
        max_length=128,
        write_only=True,
        help_text="新密码确认，请再次输入新密码",
        error_messages={
            'required': '新密码确认不能为空',
            'min_length': '新密码确认长度不能少于6位',
            'max_length': '新密码确认长度不能超过128位'
        }
    )

    def validate_new_password(self, value):
        """验证新密码强度

        检查新密码是否符合安全要求：
        - 至少包含一个字母
        - 至少包含一个数字
        - 不能包含空格

        Args:
            value (str): 新密码值

        Returns:
            str: 验证通过的新密码

        Raises:
            ValidationError: 当密码不符合安全要求时抛出
        """
        if not value:
            return value

        # 检查是否包含空格
        if ' ' in value:
            raise serializers.ValidationError("密码不能包含空格")

        # 检查是否包含至少一个字母
        if not re.search(r'[a-zA-Z]', value):
            raise serializers.ValidationError("密码必须包含至少一个字母")

        # 检查是否包含至少一个数字
        if not re.search(r'\d', value):
            raise serializers.ValidationError("密码必须包含至少一个数字")

        return value

    def validate(self, data):
        """全局验证

        验证密码修改请求的整体逻辑：
        - 新密码不能与旧密码相同
        - 新密码与新密码确认必须一致

        Args:
            data (dict): 包含所有字段数据的字典

        Returns:
            dict: 验证通过的数据

        Raises:
            ValidationError: 当验证失败时抛出
        """
        old_password = data.get('old_password')
        new_password = data.get('new_password')
        password = data.get('password')

        # 验证新密码不能与旧密码相同
        if old_password and new_password and old_password == new_password:
            raise serializers.ValidationError("新密码不能与旧密码相同")

        # 验证新密码与新密码确认必须一致
        if new_password and password and new_password != password:
            raise serializers.ValidationError("新密码与新密码确认不一致")

        return data


class WechatLoginSerializer(serializers.Serializer):
    """微信登录序列化器

    用于验证和处理微信小程序登录请求的输入数据。
    验证微信授权码、用户昵称和头像URL的格式和有效性。
    """

    js_code = serializers.CharField(
        required=True,
        max_length=128,
        help_text="微信小程序授权码，用于获取用户openid和session_key",
        error_messages={
            'required': '微信授权码不能为空',
            'max_length': '微信授权码长度不能超过128位'
        }
    )

    nickname = serializers.CharField(
        required=True,
        max_length=64,
        help_text="用户昵称，微信用户的显示名称",
        error_messages={
            'required': '用户昵称不能为空',
            'max_length': '用户昵称长度不能超过64位'
        }
    )

    avatar_url = serializers.URLField(
        required=True,
        max_length=512,
        help_text="用户头像URL，格式如 http://example.com/avatar.jpg",
        error_messages={
            'required': '用户头像URL不能为空',
            'invalid': '用户头像URL格式不正确',
            'max_length': '用户头像URL长度不能超过512位'
        }
    )

    def validate_js_code(self, value):
        """验证微信授权码格式

        检查微信授权码是否符合基本格式要求：
        - 不能包含空格
        - 不能为空字符串

        Args:
            value (str): 微信授权码

        Returns:
            str: 验证通过的微信授权码

        Raises:
            ValidationError: 当授权码格式不正确时抛出
        """
        if not value or not value.strip():
            raise serializers.ValidationError("微信授权码不能为空")

        if ' ' in value:
            raise serializers.ValidationError("微信授权码不能包含空格")

        return value.strip()

    def validate_nickname(self, value):
        """验证用户昵称格式

        检查用户昵称是否符合基本要求：
        - 不能为空字符串
        - 去除首尾空格

        Args:
            value (str): 用户昵称

        Returns:
            str: 验证通过的用户昵称

        Raises:
            ValidationError: 当昵称格式不正确时抛出
        """
        if not value or not value.strip():
            raise serializers.ValidationError("用户昵称不能为空")

        return value.strip()


class WechatRefreshSerializer(serializers.Serializer):
    """微信刷新令牌序列化器

    用于微信session_key刷新请求的序列化器。
    此接口无需额外参数，仅用于统一API格式和文档生成。
    """

    # 无需额外字段，仅通过Authorization头识别用户身份
    pass
