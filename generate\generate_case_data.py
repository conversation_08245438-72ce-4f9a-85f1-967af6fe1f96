#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
债务调解案例数据生成脚本
用途：为CaseDisplay模型生成20条债务调解成功案例数据
使用：在Django项目根目录执行 python generate_case_data.py
"""

import os
import sys
import django
import random
from datetime import date, timedelta
from decimal import Decimal

# 配置Django环境
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ops_management.settings')
django.setup()

from apps.case_display.models import CaseDisplay

# 债务调解案例名称模板
CASE_NAMES = [
    "张某与李某民间借贷纠纷调解案",
    "王某信用卡逾期债务调解案", 
    "刘某小额贷款纠纷调解案",
    "陈某与某银行房贷纠纷调解案",
    "周某消费分期付款纠纷调解案",
    "黄某与某金融公司借贷纠纷调解案",
    "赵某个人债务重组调解案",
    "孙某车贷逾期纠纷调解案",
    "杨某经营性贷款纠纷调解案",
    "马某与朋友借款纠纷调解案",
    "林某装修贷款纠纷调解案",
    "郑某教育贷款纠纷调解案",
    "吴某与某小贷公司纠纷调解案",
    "徐某房屋抵押贷款纠纷调解案",
    "丁某信用贷款逾期调解案",
    "朱某与某担保公司纠纷调解案",
    "高某汽车金融纠纷调解案",
    "袁某网贷债务调解案",
    "胡某与某银行信贷纠纷调解案",
    "秦某个体工商户贷款纠纷调解案"
]

# 调解结果模板
MEDIATION_RESULTS = [
    "经调解，债务人同意分期偿还，债权人同意减免部分利息，双方达成和解协议",
    "债务人承诺按月偿还本金，债权人免除逾期罚息，调解成功",
    "双方同意延长还款期限至24个月，利率调整为年化6%，握手言和",
    "债务人一次性偿还本金70%，余额免除，债权人表示满意",
    "经协商，债务人提供担保人，分期还款，债权人撤回起诉",
    "债务人变卖资产偿还债务80%，剩余债务分期偿还，调解达成",
    "双方同意债务重组，延期3年偿还，年利率降至4.5%",
    "债务人家属代为偿还部分债务，余额分期付清，纠纷化解",
    "经调解员耐心工作，双方重新签订还款协议，矛盾化解",
    "债务人同意每月偿还固定金额，债权人同意降低利息，达成一致"
]

# 案例详情模板
CASE_DETAILS_TEMPLATES = [
    "债务人因生意失败导致资金周转困难，无法按期偿还借款。经调解员深入了解情况，发现债务人有稳定收入来源但一时资金紧张。调解员从情理法三个角度出发，促使双方相互理解，最终达成分期还款协议。调解过程中，债务人表现出强烈的还款意愿，债权人也体现了理解和包容。",
    
    "债务人因家庭成员患重病医疗费用巨大，导致信用卡严重逾期。调解员了解到债务人家庭确实困难但品格良好，积极与银行沟通协调。最终银行同意免除部分罚息，重新制定还款计划。债务人承诺按新方案执行，并提供了收入证明和医疗费用凭证。",
    
    "债务人经营的小微企业受疫情影响，现金流断裂无法偿还贷款。调解员组织多次座谈，详细分析企业经营状况和发展前景。债权方认可企业基本面良好，同意延长还款期限并降低利率。债务人承诺企业恢复经营后优先偿还债务，并提供了详细的还款计划。",
    
    "双方因民间借贷利息计算产生分歧，债务关系日趋恶化。调解员依据相关法律法规，对利息计算进行了专业分析和说明。最终确定了合理的利息标准，债务人接受并承诺按期偿还，债权人也表示理解和支持。调解过程体现了法律的权威性和调解的灵活性。"
]

def generate_random_date():
    """生成近两年内的随机日期"""
    end_date = date.today()
    start_date = end_date - timedelta(days=730)  # 两年前
    random_days = random.randint(0, (end_date - start_date).days)
    return start_date + timedelta(days=random_days)

def generate_random_amount():
    """生成随机金额（1万-500万）"""
    amount = random.uniform(10000, 5000000)
    return Decimal(str(round(amount, 2)))

def generate_case_details():
    """生成案例详情"""
    base_template = random.choice(CASE_DETAILS_TEMPLATES)
    
    # 添加一些随机的额外信息
    additional_info = [
        "调解过程历时约2小时，双方多次交换意见。",
        "调解员采用背靠背调解方式，分别与双方深入沟通。",
        "此案例成为当地调解工作的典型示范案例。",
        "调解协议签署后，双方表示对调解结果非常满意。",
        "调解过程中注重情理并重，取得了良好的社会效果。"
    ]
    
    return base_template + random.choice(additional_info)

def create_case_data():
    """创建20条债务调解案例数据"""
    print("开始生成债务调解案例数据...")
    
    # 检查是否已存在数据
    existing_count = CaseDisplay.objects.count()
    print(f"当前数据库中已有 {existing_count} 条案例数据")
    
    confirm = input("确认要生成20条新的债务调解案例数据吗？(y/N): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return
    
    created_count = 0
    
    for i in range(20):
        try:
            case_data = CaseDisplay(
                case_name=CASE_NAMES[i],
                case_date=generate_random_date(),
                amount=generate_random_amount(),
                mediation_result=random.choice(MEDIATION_RESULTS),
                case_details=generate_case_details()
            )
            case_data.save()
            created_count += 1
            print(f"✓ 第 {created_count} 条案例数据创建成功: {case_data.case_name}")
            
        except Exception as e:
            print(f"✗ 第 {i+1} 条数据创建失败: {str(e)}")
    
    print(f"\n数据生成完成！成功创建 {created_count} 条债务调解案例数据")
    print(f"数据库中现有案例总数: {CaseDisplay.objects.count()}")

if __name__ == "__main__":
    create_case_data() 