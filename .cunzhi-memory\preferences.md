# 用户偏好设置

- 用户偏好：时间字段格式化直接修改原字段而非添加新的display字段，不生成总结性文档、测试脚本，不编译运行
- 用户明确要求：docstring重新设计时无需补充业务规则和使用场景，不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：docstring重新设计时无需补充业务规则和使用场景，不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：权限优化任务中不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：SystemUser模型字段扩展需要包含人脸核身相关字段（real_name, biz_token, detect_auth_time, detect_auth_result）和微信小程序相关字段（wechat_openid, wechat_unionid, wechat_session_key, wechat_nickname, wechat_avatar_url, wechat_bind_time），不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：微信小程序权限类创建在utils\permission_helper.py文件中，不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：WechatMiniProgramPermission权限类无需日志记录，需要重命名WxPermission和WechatMiniProgramPermission两个权限类的名称以便容易区分，不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：资产包表达式计算视图使用GenericAPIView而非APIView，不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：资产包表达式计算视图将计算结果赋值给AjaxResult的data字段，不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户要求资产包表达式计算视图增加"逻辑类型"参数：值为"文本格式化"或"结果运算"，文本格式化仅进行字符串格式化，结果运算进行格式化后使用eval计算表达式，不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户要求文件下载视图：仅创建一个通用视图，URL使用<str:method_name>参数区分不同模型，权限类使用MyPermission或WechatFaceAuthPermission，不要生成总结性Markdown文档、测试脚本，不要编译运行
