"""ops_management URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path("admin/", admin.site.urls),
    path("user/", include("apps.user.urls")),
    path("case_display/", include("apps.case_display.urls")),
    path("feedback/", include("apps.feedback.urls")),
    path("data_governance/", include("apps.data_governance.urls")),
    path("counterparty/", include("apps.counterparty.urls")),  # 对手方管理路由
    path("outbound_communication/", include("apps.outbound_communication.urls")),  # 对外通信路由
    path("mediation_management/", include("apps.mediation_management.urls")),  # 调解管理路由
    path("wechat/", include("apps.wechat.urls")),  # 微信应用路由
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

if settings.DEBUG:
    from drf_spectacular.views import (
        SpectacularAPIView,
        SpectacularRedocView,
        SpectacularSwaggerView,
    )

    urlpatterns += [
        path(
            "schema/",
            SpectacularAPIView.as_view(authentication_classes=[]),
            name="schema",
        ),
        path(
            "schema/swagger-ui/",
            SpectacularSwaggerView.as_view(authentication_classes=[]),
            name="swagger-ui",
        ),
        path(
            "schema/redoc/",
            SpectacularRedocView.as_view(authentication_classes=[]),
            name="redoc",
        ),
    ]
