#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : tasks.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

import math
import requests

# 延迟导入Django相关模块，避免在文件加载时就需要Django环境
# 这些导入将在函数内部进行，确保Django环境已经初始化
try:
    # 尝试导入Celery相关模块（在Django环境中）
    from ops_management.celery import app, CeleryResult
    from ops_management.settings import AUTH_SERVER_URL

    CELERY_AVAILABLE = True
except ImportError:
    # 如果无法导入，说明Django环境未初始化，设置标志位
    CELERY_AVAILABLE = False
    app = None
    CeleryResult = None
    AUTH_SERVER_URL = None


# 创建装饰器函数，支持条件装饰
def conditional_task_decorator(func):
    """条件任务装饰器，只在Celery可用时应用@app.task装饰器"""
    if CELERY_AVAILABLE and app:
        return app.task()(func)
    return func


@conditional_task_decorator
def sync_auth_user_local():
    """同步认证服务器用户到本地数据库"""
    try:
        # 在函数内部导入Django模型，确保Django环境已初始化
        from apps.user.models import SystemUser
        from ops_management.settings import AUTH_SERVER_URL
        from ops_management.celery import CeleryResult

        url = f"{AUTH_SERVER_URL}/user/"
        headers = {"Authorization": ""}

        # 首次请求获取总数量
        params = {"page": 1, "page_size": 100}
        response = requests.get(url, headers=headers, params=params)
        response_data = response.json().get("data", {})

        count = response_data.get("count", 0)
        total_pages = math.ceil(count / 100) if count > 0 else 1

        # 汇总所有页面的用户数据
        all_auth_system_user_info = []
        all_auth_system_user_info.extend(response_data.get("results", []))

        # 请求剩余页面的数据
        for page in range(2, total_pages + 1):
            params = {"page": page, "page_size": 100}
            response = requests.get(url, headers=headers, params=params)
            page_results = response.json().get("data", {}).get("results", [])
            all_auth_system_user_info.extend(page_results)

        all_username_lst = []

        for user_info in all_auth_system_user_info:
            if user_info.get("username") == "admin":
                continue

            # 安全获取角色信息，避免AttributeError - 参考request_auth_user_info函数逻辑
            role = {}
            role_list = user_info.get("role")
            if role_list and isinstance(role_list, list) and len(role_list) > 0:
                role = role_list[0] if isinstance(role_list[0], dict) else {}

            # 安全获取部门信息，避免AttributeError - 参考request_auth_user_info函数逻辑
            group = {}
            department_list = user_info.get("department")
            if department_list and isinstance(department_list, list) and len(department_list) > 0:
                group = department_list[0] if isinstance(department_list[0], dict) else {}

            clean_user_info = {
                "id": user_info.get("id"),
                "username": user_info.get("username"),
                "is_superuser": user_info.get("is_superuser"),
                "is_staff": user_info.get("is_staff"),
                "is_active": user_info.get("is_active"),
                "email": user_info.get("email"),
                "phone_number": user_info.get("phone_number"),
                "id_card_number": user_info.get("id_card_number"),
                "group_id": group.get("id") if group else None,
                "group_name": group.get("name") if group else None,
                "role_id": role.get("id") if role else None,
                "role_name": role.get("name") if role else None,
                # 人脸核身相关字段同步
                "real_name": user_info.get("real_name"),
                "biz_token": user_info.get("biz_token"),
                "detect_auth_time": user_info.get("detect_auth_time"),
                "detect_auth_result": user_info.get("detect_auth_result"),
                # 微信小程序相关字段同步
                "wechat_openid": user_info.get("wechat_openid"),
                "wechat_unionid": user_info.get("wechat_unionid"),
                "wechat_session_key": user_info.get("wechat_session_key"),
                "wechat_nickname": user_info.get("wechat_nickname"),
                "wechat_avatar_url": user_info.get("wechat_avatar_url"),
                "wechat_bind_time": user_info.get("wechat_bind_time"),
            }

            username = clean_user_info.pop("username")
            wechat_openid = clean_user_info.pop("wechat_openid")
            SystemUser.objects.update_or_create(
                username=username, wechat_openid=wechat_openid, defaults=clean_user_info
            )
            all_username_lst.append(username)

        if all_username_lst:
            SystemUser.objects.exclude(username__in=all_username_lst).delete()

        return CeleryResult.success("sync_auth_user_local")
    except Exception as e:
        return CeleryResult.fail("sync_auth_user_local", str(e))


@conditional_task_decorator
def sync_auth_group_local():
    """同步认证服务器部门组到本地数据库"""
    try:
        # 在函数内部导入Django模型，确保Django环境已初始化
        from apps.user.models import SystemGroup
        from ops_management.settings import AUTH_SERVER_URL
        from ops_management.celery import CeleryResult

        url = f"{AUTH_SERVER_URL}/department/"
        headers = {"Authorization": ""}
        params = {"page": 1, "page_size": 5000}
        response = requests.get(url, headers=headers, params=params)
        all_auth_system_group_info = response.json().get("data", {}).get("results", [])

        all_group_id_lst = []

        for group_info in all_auth_system_group_info:
            clean_group_info = {"id": group_info.get("id"), "name": group_info.get("name")}
            pk = clean_group_info.pop("id")
            SystemGroup.objects.update_or_create(id=pk, defaults=clean_group_info)
            all_group_id_lst.append(pk)

        if all_group_id_lst:
            SystemGroup.objects.exclude(id__in=all_group_id_lst).delete()

        return CeleryResult.success("sync_auth_group_local")
    except Exception as e:
        return CeleryResult.fail("sync_auth_group_local", str(e))


def setup_django_environment():
    """设置Django环境，用于独立运行时初始化"""
    import os
    import sys
    import django

    # 获取项目根目录路径（从 apps/user/tasks.py 向上两级到项目根目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))  # apps/user/
    project_root = os.path.dirname(os.path.dirname(current_dir))  # 项目根目录

    # 添加项目根目录到Python路径
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

    # 设置Django设置模块
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ops_management.settings")

    # 初始化Django
    django.setup()

    print("Django环境初始化完成")


if __name__ == "__main__":
    # 手动调试和测试用户同步功能
    # 可以直接运行此文件来执行用户同步，无需通过Celery任务队列

    try:
        print("正在初始化Django环境...")
        setup_django_environment()

        print("开始执行用户同步...")
        result = sync_auth_user_local()
        print(f"用户同步完成，结果：{result}")

    except Exception as e:
        print(f"执行过程中发生错误：{e}")
        import traceback

        traceback.print_exc()
