#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境变量设置帮助脚本
用于帮助用户正确设置腾讯云API密钥环境变量
"""

import os
import sys
import subprocess


def check_env_variables():
    """检查环境变量是否已正确设置"""
    secret_id = os.getenv("TENCENTCLOUD_SECRET_ID")
    secret_key = os.getenv("TENCENTCLOUD_SECRET_KEY")
    
    print("=== 腾讯云API密钥环境变量检查 ===")
    
    if secret_id:
        print(f"✓ TENCENTCLOUD_SECRET_ID: 已设置 (长度: {len(secret_id)})")
    else:
        print("✗ TENCENTCLOUD_SECRET_ID: 未设置")
    
    if secret_key:
        print(f"✓ TENCENTCLOUD_SECRET_KEY: 已设置 (长度: {len(secret_key)})")
    else:
        print("✗ TENCENTCLOUD_SECRET_KEY: 未设置")
    
    if secret_id and secret_key:
        print("\n✓ 所有必需的环境变量已正确设置！")
        return True
    else:
        print("\n✗ 存在未设置的环境变量")
        return False


def show_setup_instructions():
    """显示环境变量设置说明"""
    print("\n=== 环境变量设置说明 ===")
    print("请按照以下步骤设置环境变量：")
    print()
    
    if os.name == 'nt':  # Windows
        print("Windows 系统:")
        print("1. 临时设置（仅当前命令行窗口有效）:")
        print("   set TENCENTCLOUD_SECRET_ID=your_secret_id")
        print("   set TENCENTCLOUD_SECRET_KEY=your_secret_key")
        print()
        print("2. 永久设置（推荐）:")
        print("   - 右键 '此电脑' -> '属性' -> '高级系统设置' -> '环境变量'")
        print("   - 在 '用户变量' 或 '系统变量' 中添加上述两个变量")
        print()
        print("3. 使用 PowerShell:")
        print("   $env:TENCENTCLOUD_SECRET_ID='your_secret_id'")
        print("   $env:TENCENTCLOUD_SECRET_KEY='your_secret_key'")
    else:  # Unix/Linux/macOS
        print("Unix/Linux/macOS 系统:")
        print("1. 临时设置（仅当前终端会话有效）:")
        print("   export TENCENTCLOUD_SECRET_ID=your_secret_id")
        print("   export TENCENTCLOUD_SECRET_KEY=your_secret_key")
        print()
        print("2. 永久设置（推荐）:")
        print("   将上述 export 命令添加到 ~/.bashrc 或 ~/.zshrc 文件中")
        print("   然后执行: source ~/.bashrc 或 source ~/.zshrc")
    
    print()
    print("=== 获取密钥 ===")
    print("请访问腾讯云控制台获取您的API密钥:")
    print("https://console.cloud.tencent.com/cam/capi")
    print()
    print("=== 安全提醒 ===")
    print("- 请妥善保管您的密钥，不要泄露给他人")
    print("- 不要将密钥硬编码在代码中")
    print("- 建议定期更换密钥")


def main():
    """主函数"""
    print("腾讯云API密钥环境变量设置助手")
    print("=" * 40)
    
    # 检查当前环境变量状态
    if check_env_variables():
        print("\n您可以直接运行 sample.py 脚本了！")
    else:
        show_setup_instructions()
        print("\n设置完成后，请重新运行此脚本进行验证。")


if __name__ == "__main__":
    main()
