from django.urls import path, include
from rest_framework.routers import <PERSON>fa<PERSON><PERSON><PERSON><PERSON>
from .views.asset_package_field_config_view_set import AssetPackageFieldConfigViewSet
from .views.asset_package_management_view_set import AssetPackageManagementViewSet
from .views.data_preview_views import PreviewMappedDataView, PreviewRawDataView

router = DefaultRouter()
router.register(r"asset_package_field_config", AssetPackageFieldConfigViewSet, basename="asset_package_field_config")
router.register(r"asset_package_management", AssetPackageManagementViewSet, basename="asset_package_management")

urlpatterns = [
    # 数据预览接口
    path(
        "asset_package_management/<int:package_id>/preview_mapped_data/",
        PreviewMappedDataView.as_view(),
        name="preview_mapped_data",
    ),
    path(
        "asset_package_management/<int:package_id>/preview_raw_data/",
        PreviewRawDataView.as_view(),
        name="preview_raw_data",
    ),
    # ViewSet自动生成的路由（包含expression_calculation action）
    path("", include(router.urls)),
]
