from django.test import TestCase
from .models import Feedback


class FeedbackModelTest(TestCase):
    """
    投诉建议模型测试
    """
    
    def setUp(self):
        self.suggestion_feedback = Feedback.objects.create(
            feedback_type='suggestion',
            category='process_optimization',
            description='建议优化业务流程，提高办事效率',
            phone_number='13800138000',
            case_number='CASE2024001'
        )
        
        self.complaint_feedback = Feedback.objects.create(
            feedback_type='complaint',
            category='service_attitude',
            description='工作人员服务态度有待改善',
            phone_number='13800138001'
        )
    
    def test_suggestion_feedback_creation(self):
        """测试意见建议创建"""
        self.assertEqual(self.suggestion_feedback.feedback_type, 'suggestion')
        self.assertEqual(self.suggestion_feedback.category, 'process_optimization')
        self.assertEqual(self.suggestion_feedback.process_status, 'pending')
        self.assertEqual(self.suggestion_feedback.case_number, 'CASE2024001')
    
    def test_complaint_feedback_creation(self):
        """测试服务投诉创建"""
        self.assertEqual(self.complaint_feedback.feedback_type, 'complaint')
        self.assertEqual(self.complaint_feedback.category, 'service_attitude')
        self.assertEqual(self.complaint_feedback.process_status, 'pending')
    
    def test_feedback_str(self):
        """测试字符串表示"""
        self.assertEqual(str(self.suggestion_feedback), '意见建议: 流程优化建议')
        self.assertEqual(str(self.complaint_feedback), '服务投诉: 服务态度问题')
    
    def test_category_display(self):
        """测试类别显示"""
        self.assertEqual(self.suggestion_feedback.get_category_display(), '流程优化建议')
        self.assertEqual(self.complaint_feedback.get_category_display(), '服务态度问题') 