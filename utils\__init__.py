#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : __init__.py
<AUTHOR> JT_DA
@Date     : 2025/07/02
@File_Desc:
"""


def model_to_dict(instance):
    """
    将 Django 模型对象转换为字典

    :param instance: Django 模型对象
    :return: 字典表示的模型数据
    """
    data = {}
    for field in instance._meta.fields:
        field_name = field.name
        field_value = getattr(instance, field_name)
        data[field_name] = field_value
    return data


def sort_data_by_id(data):
    if not isinstance(data, list):
        data = [data]

    # Sort data by "id" field
    data.sort(key=lambda x: x.get("id"))

    # Recursively sort children
    for item in data:
        if "children" in item:
            item["children"] = sort_data_by_id(item["children"])

    return data
