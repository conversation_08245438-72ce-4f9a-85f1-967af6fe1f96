# Generated by Django 4.1.13 on 2025-07-22 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Feedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision', models.IntegerField(default=1, verbose_name='版本号')),
                ('created_by', models.IntegerField(null=True, verbose_name='创建人')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_by', models.IntegerField(null=True, verbose_name='更新人')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('feedback_type', models.CharField(choices=[('suggestion', '意见建议'), ('complaint', '服务投诉')], max_length=20, verbose_name='反馈类型')),
                ('case_number', models.CharField(blank=True, help_text='可选，关联的调解案件编号', max_length=100, null=True, verbose_name='关联调解案件编号')),
                ('category', models.CharField(help_text='根据反馈类型选择对应的具体类别', max_length=30, verbose_name='具体类别')),
                ('description', models.TextField(verbose_name='详细描述')),
                ('phone_number', models.CharField(help_text='反馈人的手机号码', max_length=20, verbose_name='联系方式(手机号)')),
                ('process_status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('resolved', '已解决'), ('closed', '已关闭')], default='pending', max_length=20, verbose_name='处理状态')),
                ('handler', models.CharField(blank=True, max_length=50, null=True, verbose_name='处理人')),
                ('handle_time', models.DateTimeField(blank=True, null=True, verbose_name='处理时间')),
                ('handle_result', models.TextField(blank=True, null=True, verbose_name='处理结果')),
            ],
            options={
                'verbose_name': '投诉建议',
                'verbose_name_plural': '投诉建议',
                'db_table': 't_feedback',
            },
        ),
    ]
