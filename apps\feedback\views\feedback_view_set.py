#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : feedback_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/07/10
@File_Desc: 投诉建议视图集
"""

from django.utils import timezone
from django.db import transaction
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend

from apps.feedback.models import Feedback
from apps.feedback.serializers import FeedbackSerializer, FeedbackCreateSerializer, FeedbackHandleSerializer
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult


class BaseFeedbackViewSet(viewsets.ModelViewSet):
    """
    投诉建议基础视图集，提供投诉建议管理的基础功能配置。
    """
    queryset = Feedback.objects.all().order_by("-created_time")
    serializer_class = FeedbackSerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["feedback_type", "process_status", "category"]
    search_fields = ["description", "phone_number", "case_number"]


class FeedbackViewSet(BaseFeedbackViewSet):
    """
    投诉建议管理视图集，提供投诉建议的完整生命周期管理功能。
    """
    
    def get_serializer_class(self):
        """根据action选择相应的序列化器"""
        if self.action == 'create':
            return FeedbackCreateSerializer
        elif self.action == 'handle':
            return FeedbackHandleSerializer
        else:
            return FeedbackSerializer
    
    def list(self, request, *args, **kwargs):
        """
        获取投诉建议列表

        查询并返回投诉建议的分页列表数据，支持多种过滤和搜索条件。
        主要用于投诉建议管理界面的数据展示和查询功能。

        请求参数：
            查询参数：
                page (int, 可选): 页码，默认为 1
                page_size (int, 可选): 每页数量，默认为 5，最大 9999
                feedback_type (str, 可选): 反馈类型过滤，可选值：suggestion(意见建议)、complaint(服务投诉)
                process_status (str, 可选): 处理状态过滤，可选值：pending(待处理)、processing(处理中)、resolved(已解决)、closed(已关闭)
                category (str, 可选): 具体类别过滤，根据反馈类型选择对应的类别值
                search (str, 可选): 搜索关键词，支持在描述内容、手机号码、案件编号中搜索

        响应数据结构：
            {
                "code": 200,
                "msg": "操作成功",
                "state": "success",
                "data": {
                    "links": {
                        "next": "下一页链接或null",
                        "previous": "上一页链接或null"
                    },
                    "count": 总记录数,
                    "results": [
                        {
                            "id": 记录ID,
                            "feedback_type": "反馈类型代码",
                            "feedback_type_cn": "反馈类型中文名称",
                            "case_number": "关联调解案件编号",
                            "category": "具体类别代码",
                            "category_cn": "具体类别中文名称",
                            "description": "详细描述",
                            "phone_number": "联系方式(手机号)",
                            "process_status": "处理状态代码",
                            "process_status_cn": "处理状态中文名称",
                            "handler": "处理人",
                            "handle_time": "处理时间",
                            "handle_result": "处理结果"
                        }
                    ]
                }
            }
        """
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        """
        创建新的投诉建议记录

        接收用户提交的投诉建议信息，创建新的反馈记录。
        支持意见建议和服务投诉两种类型，可选择关联调解案件编号。

        请求参数：
            请求体参数：
                feedback_type (str, 必需): 反馈类型，可选值：suggestion(意见建议)、complaint(服务投诉)
                case_number (str, 可选): 关联的调解案件编号，可为空
                category (str, 必需): 具体类别，根据反馈类型选择对应的类别值
                    - 意见建议类别：process_optimization(流程优化建议)、function_improvement(功能改进建议)、service_praise(服务表扬)、other_suggestion(其他建议)
                    - 服务投诉类别：service_attitude(服务态度问题)、processing_time(处理时间过长)、solution_inappropriate(方案不合适)、system_technical(系统技术问题)、other_complaint(其他投诉)
                description (str, 必需): 详细描述内容
                phone_number (str, 必需): 联系方式(手机号)，格式：1[3-9]xxxxxxxxx

        请求数据示例：
            {
                "feedback_type": "suggestion",
                "case_number": "CASE2024001",
                "category": "process_optimization",
                "description": "建议优化调解流程，增加在线预约功能，提高办事效率",
                "phone_number": "13800138000"
            }

        响应数据结构：
            {
                "code": 200,
                "msg": "新增成功",
                "state": "success"
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():
            serializer.save()
        return AjaxResult.success(msg="新增成功")

    def retrieve(self, request, *args, **kwargs):
        """
        获取投诉建议详细信息

        根据记录ID获取指定投诉建议的完整详细信息。
        主要用于投诉建议详情页面的数据展示和编辑表单的数据回填。

        请求参数：
            路径参数：
                id (int, 必需): 投诉建议记录的唯一标识ID

        响应数据结构：
            {
                "code": 200,
                "msg": "操作成功",
                "state": "success",
                "data": {
                    "id": 记录ID,
                    "feedback_type": "反馈类型代码",
                    "feedback_type_cn": "反馈类型中文名称",
                    "case_number": "关联调解案件编号",
                    "category": "具体类别代码",
                    "category_cn": "具体类别中文名称",
                    "description": "详细描述",
                    "phone_number": "联系方式(手机号)",
                    "process_status": "处理状态代码",
                    "process_status_cn": "处理状态中文名称",
                    "handler": "处理人",
                    "handle_time": "处理时间",
                    "handle_result": "处理结果"
                }
            }
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)

    def update(self, request, *args, **kwargs):
        """
        更新投诉建议记录信息

        根据记录ID更新指定投诉建议的基本信息。
        支持完整更新和部分更新，主要用于投诉建议信息的修改功能。

        请求参数：
            路径参数：
                id (int, 必需): 投诉建议记录的唯一标识ID
            请求体参数：
                feedback_type (str, 可选): 反馈类型，可选值：suggestion(意见建议)、complaint(服务投诉)
                case_number (str, 可选): 关联的调解案件编号
                category (str, 可选): 具体类别，根据反馈类型选择对应的类别值
                description (str, 可选): 详细描述内容
                phone_number (str, 可选): 联系方式(手机号)，格式：1[3-9]xxxxxxxxx

        请求数据示例：
            {
                "feedback_type": "complaint",
                "case_number": "CASE2024002",
                "category": "service_attitude",
                "description": "工作人员服务态度需要改善，希望加强培训",
                "phone_number": "13800138001"
            }

        响应数据结构：
            {
                "code": 200,
                "msg": "编辑成功",
                "state": "success"
            }
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():
            serializer.save()
        return AjaxResult.success(msg="编辑成功")

    def destroy(self, request, *args, **kwargs):
        """
        删除投诉建议记录

        根据记录ID永久删除指定的投诉建议记录。
        此操作不可逆，请谨慎使用，建议在删除前进行二次确认。

        请求参数：
            路径参数：
                id (int, 必需): 投诉建议记录的唯一标识ID

        响应数据结构：
            {
                "code": 200,
                "msg": "删除成功",
                "state": "success"
            }
        """
        instance = self.get_object()
        instance.delete()
        return AjaxResult.success(msg="删除成功")
    
    @action(detail=True, methods=['post'])
    def handle(self, request, pk=None):
        """
        处理投诉建议

        对指定的投诉建议进行处理操作，更新处理状态、处理人、处理时间和处理结果。
        此接口用于投诉建议的处理流程管理，支持状态流转和处理记录的完整性。

        请求参数：
            路径参数：
                id (int, 必需): 投诉建议记录的唯一标识ID
            请求体参数：
                process_status (str, 可选): 处理状态，可选值：pending(待处理)、processing(处理中)、resolved(已解决)、closed(已关闭)
                handler (str, 可选): 处理人姓名，长度不能少于2个字符
                handle_result (str, 可选): 处理结果描述

        请求数据示例：
            {
                "process_status": "resolved",
                "handler": "张三",
                "handle_result": "已联系反馈人并解决问题，优化了相关流程"
            }

        响应数据结构：
            {
                "code": 200,
                "msg": "处理成功",
                "state": "success"
            }
        """
        feedback = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():
            feedback.process_status = serializer.validated_data.get('process_status', feedback.process_status)
            feedback.handler = serializer.validated_data.get('handler', feedback.handler)
            feedback.handle_time = timezone.now()
            feedback.handle_result = serializer.validated_data.get('handle_result', feedback.handle_result)
            feedback.save()

        return AjaxResult.success(msg="处理成功")
    
    @action(detail=False, methods=['get'])
    def categories(self, request):
        """
        获取投诉建议相关的所有类别选项

        返回投诉建议模块中使用的所有枚举选项，包括反馈类型、具体类别和处理状态。
        主要用于前端表单的下拉选项数据源和数据字典功能。

        响应数据结构：
            {
                "code": 200,
                "msg": "操作成功",
                "state": "success",
                "data": {
                    "feedback_types": {
                        "suggestion": "意见建议",
                        "complaint": "服务投诉"
                    },
                    "suggestion_categories": {
                        "process_optimization": "流程优化建议",
                        "function_improvement": "功能改进建议",
                        "service_praise": "服务表扬",
                        "other_suggestion": "其他建议"
                    },
                    "complaint_categories": {
                        "service_attitude": "服务态度问题",
                        "processing_time": "处理时间过长",
                        "solution_inappropriate": "方案不合适",
                        "system_technical": "系统技术问题",
                        "other_complaint": "其他投诉"
                    },
                    "process_status_choices": {
                        "pending": "待处理",
                        "processing": "处理中",
                        "resolved": "已解决",
                        "closed": "已关闭"
                    }
                }
            }
        """
        data = {
            'feedback_types': dict(Feedback.FEEDBACK_TYPE_CHOICES),
            'suggestion_categories': dict(Feedback.SUGGESTION_CATEGORY_CHOICES),
            'complaint_categories': dict(Feedback.COMPLAINT_CATEGORY_CHOICES),
            'process_status_choices': dict(Feedback.PROCESS_STATUS_CHOICES),
        }

        return AjaxResult.success(data=data)