#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : tasks.py
<AUTHOR> JT_DA
@Date     : 2025/07/29
@File_Desc: 调解管理异步任务，处理批量案件状态更新等后台任务
"""

import logging
from typing import Dict, List

# 延迟导入Django相关模块，避免在文件加载时就需要Django环境
try:
    # 尝试导入Celery相关模块（在Django环境中）
    from ops_management.celery import app, CeleryResult

    CELERY_AVAILABLE = True
except ImportError:
    # 如果无法导入，说明Django环境未初始化，设置标志位
    CELERY_AVAILABLE = False
    app = None
    CeleryResult = None

# 获取日志记录器
logger = logging.getLogger(__name__)


# 创建装饰器函数，支持条件装饰
def conditional_task_decorator(func):
    """条件任务装饰器，只在Celery可用时应用@app.task装饰器"""
    if CELERY_AVAILABLE and app:
        return app.task()(func)
    return func


@conditional_task_decorator
def batch_update_mediation_case_status_async(case_ids: List[int]) -> Dict:
    """
    异步批量更新调解案件状态

    该任务在后台异步执行，适用于大批量案件状态更新操作。主要功能：
    1. 批量将案件状态从"待发起"更新为"已发起"
    2. 设置案件的发起时间
    3. 返回详细的更新结果

    Args:
        case_ids (List[int]): 需要更新状态的案件ID列表

    Returns:
        Dict: Celery任务执行结果，包含更新数量和案件详情
    """
    logger.info(f"开始异步批量更新调解案件状态，案件数量: {len(case_ids)}")

    try:
        # 在函数内部导入，确保Django环境已初始化
        from django.db import transaction
        from django.utils import timezone
        from apps.mediation_management.models import MediationCase
        from ops_management.celery import CeleryResult

        with transaction.atomic():
            # 批量更新案件状态和发起时间
            updated_count = MediationCase.objects.filter(
                id__in=case_ids,
                case_status='draft'  # 确保只更新待发起状态的案件
            ).update(
                case_status='initiated',
                initiate_date=timezone.now()
            )
            
            # 获取更新后的案件信息用于返回
            updated_cases = MediationCase.objects.filter(
                id__in=case_ids
            ).values('id', 'case_number', 'case_status', 'initiate_date')
            
            result = {
                'updated_count': updated_count,
                'updated_cases': list(updated_cases),
                'total_requested': len(case_ids)
            }

        logger.info(f"异步批量更新调解案件状态完成，更新数量: {updated_count}/{len(case_ids)}")

        # 返回成功结果
        return CeleryResult.success("batch_update_mediation_case_status_async")

    except Exception as e:
        logger.error(f"异步批量更新调解案件状态失败: {str(e)}")
        # 返回失败结果
        return CeleryResult.fail("batch_update_mediation_case_status_async", str(e))


if __name__ == "__main__":
    """
    调试和测试异步任务功能
    可以直接运行此文件来测试异步任务，无需通过Celery任务队列
    """
    import os
    import sys
    import django

    # 设置Django环境
    def setup_django_environment():
        """设置Django环境，用于调试和测试"""
        # 添加项目根目录到Python路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        sys.path.insert(0, project_root)
        
        # 设置Django设置模块
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ops_management.settings')
        
        # 初始化Django
        django.setup()

    print("=" * 60)
    print("调解管理异步任务调试测试")
    print("=" * 60)

    # 检查Celery是否可用
    if CELERY_AVAILABLE:
        print("✅ Celery环境可用")

        # 测试异步任务
        test_case_ids_input = input("请输入测试用的案件ID列表（逗号分隔）: ").strip()

        if test_case_ids_input:
            try:
                # 解析案件ID列表
                test_case_ids = [int(id.strip()) for id in test_case_ids_input.split(',') if id.strip()]
                
                if test_case_ids:
                    print(f"\n正在测试异步任务...")
                    print(f"案件ID列表: {test_case_ids}")

                    try:
                        # 初始化Django环境
                        setup_django_environment()
                        
                        # 直接调用函数（同步方式，用于测试）
                        result = batch_update_mediation_case_status_async(test_case_ids)
                        print(f"✅ 任务执行成功: {result}")
                    except Exception as e:
                        print(f"❌ 任务执行失败: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print("❌ 案件ID列表为空，跳过测试")
            except ValueError as e:
                print(f"❌ 案件ID格式错误: {e}")
        else:
            print("❌ 测试参数不完整，跳过测试")
    else:
        print("❌ Celery环境不可用，请检查Django环境和Celery配置")

    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
