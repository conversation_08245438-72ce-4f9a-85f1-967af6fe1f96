# -*- coding: utf-8 -*-
"""
语音外呼记录序列化器
"""
from rest_framework import serializers
from apps.outbound_communication.models import VoiceCallRecord


class VoiceCallRecordListSerializer(serializers.ModelSerializer):
    """语音外呼记录列表序列化器"""

    # 呼叫状态中文显示
    call_status_cn = serializers.CharField(source="get_call_status_display", read_only=True)

    # 债权人名称显示
    creditor_name = serializers.CharField(source="creditor.creditor_name", read_only=True)

    # 债务人名称显示
    debtor_name = serializers.CharField(source="debtor.debtor_name", read_only=True)

    # 呼叫开始时间格式化显示
    call_start_time = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)

    # 呼叫结束时间格式化显示
    call_end_time = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)

    # 通话时长格式化显示 - 转换为分钟:秒格式
    call_duration_display = serializers.SerializerMethodField()

    # 录音文件名显示 - 从路径中提取文件名
    recording_file_name = serializers.SerializerMethodField()

    class Meta:
        model = VoiceCallRecord
        fields = [
            "id",
            "called_number",
            "caller_number",
            "call_status",
            "call_status_cn",
            "call_duration",
            "call_duration_display",
            "call_start_time",
            "call_end_time",
            "recording_file_path",
            "recording_file_name",
            "task_batch_id",
            "creditor",
            "creditor_name",
            "debtor",
            "debtor_name",
            "call_result_notes",
        ]

    def get_call_duration_display(self, obj):
        """获取通话时长格式化显示 - 转换为分钟:秒格式"""
        if not obj.call_duration or obj.call_duration == 0:
            return "00:00"

        minutes = obj.call_duration // 60
        seconds = obj.call_duration % 60
        return f"{minutes:02d}:{seconds:02d}"

    def get_recording_file_name(self, obj):
        """获取录音文件名 - 从路径中提取文件名"""
        if not obj.recording_file_path:
            return "无录音文件"

        import os

        return os.path.basename(obj.recording_file_path)
