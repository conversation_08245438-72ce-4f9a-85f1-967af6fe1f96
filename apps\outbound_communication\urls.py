# -*- coding: utf-8 -*-
"""
对外通信应用URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from apps.outbound_communication.views import VoiceCallRecordViewSet, SmsRecordViewSet

# 创建路由器实例
router = DefaultRouter()

# 注册视图集
router.register(r'voice_call_records', VoiceCallRecordViewSet, basename='voice_call_record')
router.register(r'sms_records', SmsRecordViewSet, basename='sms_record')

urlpatterns = [
    # 对外通信相关接口
    path('', include(router.urls)),
]
