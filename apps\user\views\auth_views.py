#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : auth_views.py
<AUTHOR> JT_DA
@Date     : 2025/07/03
@File_Desc:
"""

import logging
import requests
from rest_framework.views import APIView
from rest_framework.generics import GenericAPIView
from rest_framework import serializers
from rest_framework.permissions import IsAuthenticated

from ops_management.settings import OAUTH2_PROVIDER, AUTH_SERVER_URL
from utils.ajax_result import AjaxResult
from utils.user_helper import request_auth_user_info
from apps.user.serializers import ChangePasswordSerializer

# 获取日志记录器
logger = logging.getLogger(__name__)


class UserInfo(APIView):
    """
    用户信息获取视图

    通过访问令牌获取当前登录用户的详细信息，包括用户基本信息、部门信息和角色信息。
    主要用于前端获取当前用户的身份信息和权限数据，支持用户界面的个性化展示。
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        获取当前用户信息

        通过请求头中的Authorization令牌获取用户详细信息。
        验证用户身份并返回包含用户基本信息、部门和角色的完整数据。

        **请求参数：**
        **请求头参数：**
        - Authorization (字符串, 必需): 用户访问令牌，格式为 "Bearer token_string"

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "state": "success",
            "data": {
                "id": 1,
                "username": "admin",
                "role_id": 1,
                "role_name": "管理员",
                "group_id": 1,
                "group_name": "系统管理部",
                "phone_number": "13800138000",
                "email": "<EMAIL>",
                "is_active": true
            }
        }
        ```

        **错误响应示例：**
        ```json
        {
            "code": 401,
            "msg": "未授权",
            "state": "fail"
        }
        ```
        """
        access_token = request.headers.get("Authorization")
        if not access_token:
            return AjaxResult.unauthorized()

        user_info = request_auth_user_info(request, access_token)
        # if not all([user_info.get("group_id"), user_info.get("role_id")]):
        #     return AjaxResult.fail()

        return AjaxResult.success(data=user_info)


class GetNewAccessToken(APIView):
    """
    访问令牌刷新视图

    使用刷新令牌获取新的访问令牌，延长用户会话有效期。
    当访问令牌即将过期时，前端可调用此接口获取新的令牌，避免用户重新登录。
    """

    def get(self, request):
        """
        刷新访问令牌

        通过刷新令牌获取新的访问令牌，延长用户会话时间。
        需要提供有效的刷新令牌和当前的Authorization头信息。

        **请求参数：**
        **请求头参数：**
        - Authorization (字符串, 必需): 当前用户访问令牌，格式为 "Bearer token_string"

        **查询参数：**
        - refresh_token (字符串, 必需): 刷新令牌，用于获取新的访问令牌

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "state": "success",
            "data": {
                "access_token": "new_access_token_string",
                "refresh_token": "new_refresh_token_string",
                "expires_in": 3600,
                "token_type": "Bearer"
            }
        }
        ```

        **错误响应示例：**
        ```json
        {
            "code": 401,
            "msg": "未授权",
            "state": "fail"
        }
        ```
        """
        ori_auth_token = request.headers.get("Authorization")
        refresh_token = request.GET.get("refresh_token")
        if not refresh_token:
            return AjaxResult.unauthorized()

        client_id, client_secret = OAUTH2_PROVIDER.get("RESOURCE_SERVER_INTROSPECTION_CREDENTIALS")
        response = requests.post(
            f"{AUTH_SERVER_URL}/token/",
            headers={"Authorization": ori_auth_token},
            data={
                "refresh_token": refresh_token,
                "client_id": client_id,
                "client_secret": client_secret,
            },
        )

        if response.status_code == 200 and (response_data := response.json().get("data")):
            return AjaxResult.success(data=response_data)
        else:
            return AjaxResult.unauthorized()


class UserLogout(APIView):
    """
    用户退出登录接口

    调用认证服务器的退出接口，注销用户的访问令牌。
    无论认证服务器返回什么状态，都统一返回未授权状态，
    确保前端能够正确处理退出登录的逻辑。
    """

    def post(self, request):
        """
        处理用户退出登录请求

        从请求头中获取Authorization token，调用认证服务器的退出接口。
        无论调用结果如何，都返回未授权状态，确保用户会话被清理。

        Returns:
            JsonResponse: 始终返回401未授权状态
        """
        # 从请求头获取认证token
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            logger.warning("退出登录请求缺少Authorization头")
            return AjaxResult.unauthorized()

        # 处理Bearer token格式，提取实际token
        token = auth_header
        if auth_header.startswith("Bearer "):
            token = auth_header[7:]  # 移除"Bearer "前缀

        try:
            # 调用认证服务器退出接口
            logout_url = f"{AUTH_SERVER_URL}/logout/"
            response = requests.post(logout_url, json={"token": token}, timeout=5)  # 设置5秒超时

            # 记录退出操作日志
            logger.info(f"用户退出登录，认证服务器响应状态码: {response.status_code}")

        except requests.exceptions.RequestException as e:
            # 记录调用认证服务器失败的情况
            logger.error(f"调用认证服务器退出接口失败: {str(e)}")
        except Exception as e:
            # 记录其他异常情况
            logger.error(f"退出登录过程中发生异常: {str(e)}")

        # 无论认证服务器返回什么状态码，都统一返回未授权状态
        # 这确保前端能够正确处理退出登录逻辑
        return AjaxResult.unauthorized()


class ChangePassword(GenericAPIView):
    """
    用户密码修改接口

    调用认证服务器的密码修改接口，为用户提供修改密码的功能。
    需要提供旧密码、新密码和新密码确认三个参数进行验证和修改。
    """

    # 配置序列化器类
    serializer_class = ChangePasswordSerializer

    def post(self, request):
        """
        处理用户密码修改请求，支持密码强度验证和新密码确认验证。
        调用认证服务器的密码修改接口，为用户提供安全的密码修改功能。

        **请求参数：**
        **请求体参数：**
        - old_password (字符串, 必需): 旧密码，用于验证用户身份，长度6-128位
        - new_password (字符串, 必需): 新密码，用户希望设置的新密码，长度6-128位，必须包含字母和数字
        - password (字符串, 必需): 新密码确认，请再次输入新密码，必须与new_password一致

        **请求数据示例：**
        ```json
        {
            "old_password": "oldpass123",
            "new_password": "newpass456",
            "password": "newpass456"
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 401,
            "msg": "未授权",
            "state": "fail",
            "data": "http://192.168.1.107:9401/authsage"
        }
        ```

        **业务说明：**
        密码修改成功后返回401未授权状态，强制用户重新登录以确保安全。
        这是安全最佳实践，确保新密码立即生效，避免使用旧的认证令牌继续操作。

        **错误响应示例：**
        ```json
        {
            "code": 400,
            "msg": "新密码与新密码确认不一致",
            "state": "fail"
        }
        ```
        """
        # 使用序列化器验证请求数据
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as e:
            # 记录验证失败日志
            logger.warning(f"密码修改请求参数验证失败: {e}")
            # 提取第一个错误信息返回给用户
            error_msg = "参数验证失败"
            if hasattr(e, "detail"):
                if isinstance(e.detail, dict):
                    # 获取第一个字段的第一个错误信息
                    for field_errors in e.detail.values():
                        if isinstance(field_errors, list) and field_errors:
                            error_msg = str(field_errors[0])
                            break
                elif isinstance(e.detail, list) and e.detail:
                    error_msg = str(e.detail[0])
            return AjaxResult.fail(msg=error_msg)

        # 从请求头获取认证token
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            logger.warning("密码修改请求缺少Authorization头")
            return AjaxResult.unauthorized()

        # 获取验证通过的数据
        validated_data = serializer.validated_data
        password_data = {
            "old_password": validated_data["old_password"],
            "new_password": validated_data["new_password"],
            "password": validated_data["password"],
        }

        try:
            # 调用认证服务器密码修改接口
            change_password_url = f"{AUTH_SERVER_URL}/set_password/"
            response = requests.post(
                change_password_url,
                headers={"Authorization": auth_header, "Content-Type": "application/json"},
                json=password_data,
                timeout=5,  # 设置5秒超时
            )

            # 记录操作日志
            logger.info(f"用户密码修改请求，认证服务器响应状态码: {response.status_code}")

            # 根据响应状态码返回相应结果
            response_data = response.json()
            if response_data.get("code") == 200:
                logger.info("用户密码修改成功")
                logger.info("密码修改成功，返回未授权状态强制用户重新登录以确保安全")
                return AjaxResult.unauthorized()
            else:
                # 认证服务器返回业务错误
                error_msg = response_data.get("msg", "密码修改失败")
                logger.warning(f"密码修改失败: {error_msg}")
                return AjaxResult.fail(msg=error_msg)

        except requests.exceptions.Timeout:
            # 请求超时处理
            logger.error("调用认证服务器密码修改接口超时")
            return AjaxResult.fail(msg="请求超时，请稍后重试")
        except requests.exceptions.RequestException as e:
            # 网络请求异常处理
            logger.error(f"调用认证服务器密码修改接口失败: {str(e)}")
            return AjaxResult.fail(msg="网络连接异常，请稍后重试")
        except Exception as e:
            # 其他异常处理
            logger.error(f"密码修改过程中发生异常: {str(e)}")
            return AjaxResult.server_error(msg="服务器内部错误，请联系管理员")
