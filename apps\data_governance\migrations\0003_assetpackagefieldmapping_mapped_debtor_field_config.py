# Generated by Django 4.1.13 on 2025-07-26 18:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('data_governance', '0002_alter_assetpackagemanagement_file_size_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='assetpackagefieldmapping',
            name='mapped_debtor_field_config',
            field=models.CharField(blank=True, choices=[('debtor_type', '债务人类型'), ('debtor_name', '债务人名称'), ('id_type', '证件类型'), ('id_number', '证件号码'), ('phones', '联系方式-电话'), ('emails', '联系方式-邮箱'), ('addresses', '联系方式-地址'), ('wechats', '联系方式-微信')], help_text='选择映射到债务人模型中的具体字段，用于数据导入时的字段对应关系', max_length=50, null=True, verbose_name='映射的债务人字段配置'),
        ),
    ]
