#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : gunicorn_config.py
<AUTHOR> JT_DA
@Date     : 2025/06/27
@File_Desc: 
"""
bind = "0.0.0.0:14010"
workers = 1
worker_class = "gevent"
reload = False


env = {
    # 是否开启debug模式，该模式主要用于调试
    "debug": True,
    "log_level": "INFO",
    "log_dir": "./logs",
    # 上传文件存储地址
    "upload_dir": "./upload",
    # 备份文件存储地址
    "backup_dir": "./backups",
    # 用户系统服务（后端）
    "auth_server_url": "http://*************:14001",
    # 用户系统服务（前端）
    "auth_server_frontend_url": "http://*************:9401/authsage",
    # 数据库配置
    "db_engine": "django.db.backends.mysql",
    "db_host": "**************",
    "db_port": "13306",
    "db_user": "root",
    "db_password": "root_user",
    "db_name": "ops_management_htm",
    # 对接用户系统字段
    "client_id": "Crl8ANJxKlKVMseB4QBBDVqRe286VhPSy8AdxYFg",
    "client_secret": "BAp88AVl6lGalHqQLRFFU8P8kaQNl3XK3EDvMXq2r1qrbTO0QQqo6Yh4lS5HyNaLjtuRlFHZT0vHZjzqRKviNC9KBwHPsMPRfOLB6GBYmVn9sZOPdtxYPKThUqtCNoX0",
    # ip白名单
    "ip_white_list": "127.0.0.1,************,*************",  # 逗号分隔的ip列表
    # celery broker url
    "celery_broker_url": "redis://**************:16379/14",
    "celeryd_concurrency": 4,
    # cache url
    "cache_url": "redis://**************:16379/14",
    # 缓存配置
    "enable_cache": True,
    "cache_timeout": 60 * 15,
    "redis_host": "**************",
    "redis_port": "16379",
    "redis_db": "14",
    # OAuth2 资源服务器安全配置
    "oauth2_security_enabled": False, # 是否启用OAuth2安全配置（总开关）
    "oauth2_https_required": True,    # 是否要求HTTPS（生产环境建议True）
    "oauth2_cors_enabled": True,      # 是否启用CORS配置
    "cors_allowed_origins": "http://*************:9401,http://localhost:8080",  # 允许的跨域来源
    "cors_allow_credentials": True,   # 是否允许跨域凭据
    # 人脸核身
    "tencent_faceid_rule_id": "1",
    "tencent_faceid_redirect_url": "https://prod-9gwr0pqvc081f7f4-1370735801.tcloudbaseapp.com/"
}
