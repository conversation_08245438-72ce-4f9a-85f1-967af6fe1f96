#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediator_views.py
<AUTHOR> JT_DA
@Date     : 2025/07/21
@File_Desc: 调解员视图
"""

from rest_framework.views import APIView

from apps.user.models import SystemUser
from apps.user.serializers import SystemUserSerializer
from utils.ajax_result import AjaxResult


class MediatorListView(APIView):
    """
    调解员列表视图

    获取系统中所有角色为"调解员"的用户信息列表。
    主要用于调解案件分配、调解员选择等业务场景的数据源支持。
    """

    def get(self, request):
        """
        获取调解员列表

        查询并返回系统中所有角色名称为"调解员"的用户信息列表。
        返回调解员的基本信息，包括用户ID、用户名、角色、部门、联系方式等。

        **请求参数：**
        - 无需任何参数

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "state": "success",
            "data": [
                {
                    "id": 1,
                    "username": "mediator001",
                    "role_id": 2,
                    "role_name": "调解员",
                    "group_id": 3,
                    "group_name": "调解部",
                    "phone_number": "13800138001",
                    "email": "<EMAIL>",
                    "is_active": true
                }
            ]
        }
        ```

        **错误响应示例：**
        ```json
        {
            "code": 500,
            "msg": "获取调解员列表失败: 数据库连接异常",
            "state": "fail",
            "data": null
        }
        ```
        """
        try:
            # 查询角色名称为"调解员"的所有用户
            mediators = SystemUser.objects.filter(role_name="调解员")
            
            # 使用序列化器处理数据
            serializer = SystemUserSerializer(mediators, many=True)
            
            # 返回成功响应
            return AjaxResult.success(data=serializer.data)
            
        except Exception as e:
            # 异常处理，返回错误响应
            return AjaxResult.fail(msg=f"获取调解员列表失败: {str(e)}")
