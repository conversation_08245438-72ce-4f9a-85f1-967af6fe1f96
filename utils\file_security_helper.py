#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : file_security_helper.py
<AUTHOR> JT_DA
@Date     : 2025/07/31
@File_Desc: 文件安全辅助工具，提供安全的文件下载链接生成和权限验证功能
"""

import logging
from django.urls import reverse
from django.conf import settings
from apps.data_governance.models import AssetPackageManagementFile
from apps.mediation_management.models import MediationCaseFile

# 获取日志记录器
logger = logging.getLogger(__name__)


class FileSecurityHelper:
    """
    文件安全辅助类，提供安全的文件下载链接生成和权限验证功能。
    该类封装了文件安全标识符的管理逻辑，确保文件下载的安全性。
    """

    # 支持的文件模型映射
    SUPPORTED_MODELS = {
        'asset_package_file': AssetPackageManagementFile,
        'mediation_case_file': MediationCaseFile,
    }

    @classmethod
    def generate_secure_download_url(cls, file_instance):
        """
        为文件实例生成安全的下载链接，使用UUID安全标识符替代直接文件路径。
        
        Args:
            file_instance: 文件模型实例（AssetPackageManagementFile 或 MediationCaseFile）
            
        Returns:
            str: 安全的下载URL，如果文件实例无效则返回None
            
        Example:
            url = FileSecurityHelper.generate_secure_download_url(file_obj)
            # 返回: "/user/files/download/550e8400-e29b-41d4-a716-************/"
        """
        try:
            # 验证文件实例是否有效
            if not file_instance or not hasattr(file_instance, 'secure_token'):
                logger.warning("文件实例无效或缺少安全标识符")
                return None
                
            # 验证文件实例类型是否支持
            if not isinstance(file_instance, tuple(cls.SUPPORTED_MODELS.values())):
                logger.warning(f"不支持的文件模型类型: {type(file_instance)}")
                return None
                
            # 验证安全标识符是否存在
            if not file_instance.secure_token:
                logger.warning(f"文件实例缺少安全标识符: {file_instance.id}")
                return None
                
            # 生成安全下载URL
            secure_url = reverse('secure_file_download', kwargs={'secure_token': file_instance.secure_token})
            
            logger.debug(f"生成安全下载链接: {secure_url} for file {file_instance.id}")
            return secure_url
            
        except Exception as e:
            logger.error(f"生成安全下载链接失败: {str(e)}")
            return None

    @classmethod
    def get_file_by_secure_token(cls, secure_token):
        """
        通过安全标识符获取文件实例，支持多种文件模型的查找。
        
        Args:
            secure_token (UUID): 文件安全标识符
            
        Returns:
            tuple: (file_instance, model_name) 如果找到文件，否则返回 (None, None)
            
        Example:
            file_obj, model_name = FileSecurityHelper.get_file_by_secure_token(token)
        """
        try:
            # 遍历支持的模型，查找匹配的文件记录
            for model_name, model_class in cls.SUPPORTED_MODELS.items():
                try:
                    file_instance = model_class.objects.get(secure_token=secure_token)
                    logger.debug(f"找到文件记录: {model_name} secure_token={secure_token}")
                    return file_instance, model_name
                except model_class.DoesNotExist:
                    continue
                    
            # 如果在所有模型中都未找到文件记录
            logger.warning(f"未找到文件记录: secure_token={secure_token}")
            return None, None
            
        except Exception as e:
            logger.error(f"查找文件记录失败: {str(e)}")
            return None, None

    @classmethod
    def validate_file_access_permission(cls, file_instance, user):
        """
        验证用户是否有权限访问指定文件，可根据业务需求扩展权限逻辑。
        
        Args:
            file_instance: 文件模型实例
            user: 用户实例
            
        Returns:
            bool: True表示有权限访问，False表示无权限
            
        Note:
            当前实现为基础版本，可根据具体业务需求扩展权限验证逻辑
        """
        try:
            # 基础权限检查：用户必须已认证
            if not user or not user.is_authenticated:
                logger.warning("用户未认证，拒绝文件访问")
                return False
                
            # 可根据业务需求添加更多权限检查逻辑
            # 例如：检查用户是否属于文件关联的项目、部门等
            
            logger.debug(f"用户 {user.username} 通过文件访问权限验证")
            return True
            
        except Exception as e:
            logger.error(f"文件访问权限验证失败: {str(e)}")
            return False

    @classmethod
    def get_file_info_for_response(cls, file_instance):
        """
        获取文件信息用于HTTP响应，提取安全的文件元数据。
        
        Args:
            file_instance: 文件模型实例
            
        Returns:
            dict: 包含文件信息的字典，如果文件无效则返回None
        """
        try:
            if not file_instance:
                return None
                
            # 提取安全的文件信息
            file_info = {
                'secure_token': str(file_instance.secure_token),
                'original_filename': getattr(file_instance, 'file_name', 'unknown'),
                'file_size': file_instance.file.size if file_instance.file else 0,
                'created_time': file_instance.created_time.isoformat() if hasattr(file_instance, 'created_time') else None,
            }
            
            return file_info
            
        except Exception as e:
            logger.error(f"获取文件信息失败: {str(e)}")
            return None
