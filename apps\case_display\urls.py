#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : urls.py
<AUTHOR> JT_DA
@Date     : 2025/07/10
@File_Desc: 案例展示URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from apps.case_display.views.case_display_view_set import CaseDisplayViewSet

# 创建路由器
router = DefaultRouter()

# 注册ViewSet
router.register(r"case_display", CaseDisplayViewSet, basename="case_display")

urlpatterns = [
    # 案例展示相关接口
    path("", include(router.urls)),
]
