#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : exception_helper.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc: OAuth2增强的异常处理器
"""
from rest_framework.views import exception_handler
from rest_framework.response import Response
from ops_management.settings import AUTH_SERVER_FRONTEND_URL

def custom_exception_handler(exc, context):
    # 先调用默认的异常处理器获取标准的响应
    response = exception_handler(exc, context)

    if response is not None:
        # 在标准响应的基础上自定义返回内容
        custom_data = {"code": response.status_code, "msg": exc.detail, "state": "fail"}
        
        # OAuth2 认证错误处理
        if response.status_code == 401:
            custom_data["data"] = AUTH_SERVER_FRONTEND_URL
            # 检查是否是OAuth2相关错误
            if hasattr(exc, 'detail') and isinstance(exc.detail, dict):
                if 'invalid_token' in str(exc.detail):
                    custom_data["msg"] = "访问令牌无效或已过期，请重新登录"
                elif 'insufficient_scope' in str(exc.detail):
                    custom_data["msg"] = "访问权限不足，请联系管理员"
            else:
                custom_data["msg"] = "身份验证失败，请重新登录"
                
        # OAuth2 权限错误处理
        elif response.status_code == 403:
            if hasattr(exc, 'detail') and 'scope' in str(exc.detail).lower():
                custom_data["msg"] = "访问此资源需要更高的权限级别"
            
        response.data = custom_data
        # 使用Response对象返回自定义响应
        return Response(custom_data, status=response.status_code)

    return response
