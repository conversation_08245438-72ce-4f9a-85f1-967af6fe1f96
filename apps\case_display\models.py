from django.db import models
from ops_management.base_model import BaseModel

# Create your models here.

class CaseDisplay(BaseModel):
    """案例展示模型"""
    case_name = models.CharField(max_length=255, blank=True, null=True, verbose_name="案例名称")
    case_date = models.DateField(blank=True, null=True, verbose_name="案例日期")  
    amount = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True, verbose_name="涉及金额")
    mediation_result = models.TextField(blank=True, null=True, verbose_name="调解结果")
    case_details = models.TextField(blank=True, null=True, verbose_name="案例详情")
    
    class Meta:
        verbose_name = "案例展示"
        verbose_name_plural = "案例展示"
        db_table = "t_case_display"
        
    def __str__(self):
        return self.case_name or f"案例-{self.id}"
