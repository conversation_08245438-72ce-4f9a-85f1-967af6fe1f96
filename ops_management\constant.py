#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : constant.py
<AUTHOR> JT_DA
@Date     : 2025/07/03
@File_Desc: 系统常量定义
"""

import enum
from utils.validator_helper import validate_phone_number, validate_id_card, validate_social_credit_code


class RedisKey(enum.Enum):
    AUTH_USER_INFO = "auth_user_info"
    VERIFIED_TOKEN = "verified_token"
    DICTIONARY = "dictionary"


# 数据验证选择项与验证函数的映射关系
# 用于将 AssetPackageFieldConfig.VALIDATION_CHOICES 中的选项映射到具体的验证函数
VALIDATION_FUNCTION_MAPPING = {
    "none": None,  # 无校验
    "phone": validate_phone_number,  # 手机号格式校验
    "id_card": validate_id_card,  # 身份证格式校验
    "social_credit_code": validate_social_credit_code,  # 统一社会信用代码校验
}
