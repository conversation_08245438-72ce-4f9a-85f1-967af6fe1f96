#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : user_check.py
<AUTHOR> JT_DA
@Date     : 2025/06/27
@File_Desc: 
"""

from django.conf import settings
from ipware import get_client_ip


def is_admin(request):
    """
    检查用户是否是管理员

    :param request: 请求对象
    :return: True or False
    """
    user = request.user
    return user.is_authenticated and user.is_superuser


def is_staff(request):
    """
    检查用户是否是员工

    :param request: 请求对象
    :return: True or False
    """
    user = request.user
    return user.is_authenticated and user.is_staff


# IP白名单可访问
def is_ip_white(request):
    """
    检查用户是否是IP白名单

    :param request: 请求对象
    :return: True or False
    """
    user = request.user
    client_ip, _ = get_client_ip(request)
    ip_white_list = settings.IP_WHITE_LIST
    return user.is_authenticated and (
        client_ip in ip_white_list or client_ip.startswith("172")
    )
