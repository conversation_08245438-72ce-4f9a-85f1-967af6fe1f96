# -*- coding: utf-8 -*-
"""
语音外呼记录视图集
"""
from rest_framework import viewsets
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend

from apps.outbound_communication.models import VoiceCallRecord
from apps.outbound_communication.serializers import VoiceCallRecordListSerializer
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult


class BaseVoiceCallRecordViewSet(viewsets.ReadOnlyModelViewSet):
    """
    语音外呼记录基础ViewSet

    提供语音外呼记录的基础只读操作功能，包括列表查询、详情查看、过滤和搜索。
    所有语音外呼记录按呼叫开始时间倒序排列，支持分页展示。
    """

    queryset = VoiceCallRecord.objects.all().order_by("-call_start_time")
    serializer_class = VoiceCallRecordListSerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["call_status", "creditor", "debtor", "task_batch_id"]  # 支持按呼叫状态、债权人、债务人、批次号过滤
    search_fields = ["called_number", "caller_number", "call_result_notes"]  # 支持按号码和备注搜索


class VoiceCallRecordViewSet(BaseVoiceCallRecordViewSet):
    """
    语音外呼记录管理ViewSet

    提供语音外呼记录的完整管理功能，包括分页列表查询和详情查看。
    支持多维度过滤、模糊搜索和完整的关联信息展示。
    """

    def list(self, request, *args, **kwargs):
        """
        获取语音外呼记录列表

        提供语音外呼记录的分页查询功能，支持按呼叫状态、关联对象等多维度过滤，
        以及按号码和备注内容进行模糊搜索。记录按呼叫开始时间倒序排列。

        **请求参数**
        查询参数：
        - call_status (string, 可选): 呼叫状态过滤，可选值：not_connected(未接通)、connected(已接通)、busy(忙线)、power_off(关机)、no_answer(无人接听)、invalid_number(无效号码)、rejected(拒接)、failed(呼叫失败)
        - creditor (integer, 可选): 债权人ID，过滤指定债权人的外呼记录
        - debtor (integer, 可选): 债务人ID，过滤指定债务人的外呼记录
        - task_batch_id (string, 可选): 外呼任务批次号，过滤指定批次的记录
        - search (string, 可选): 搜索关键词，支持按被叫号码、主叫号码、外呼结果备注进行模糊搜索
        - page (integer, 可选): 页码，默认为1
        - page_size (integer, 可选): 每页记录数，默认为20，最大100

        **请求数据示例**
        该接口为GET请求，无需请求体。

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "success",
            "data": {
                "count": 150,
                "next": "http://example.com/api/voice_call_records/?page=2",
                "previous": null,
                "results": [
                    {
                        "id": 1,
                        "called_number": "13800138000",
                        "caller_number": "02088888888",
                        "call_status": "connected",
                        "call_status_cn": "已接通",
                        "call_duration": 120,
                        "call_duration_display": "02:00",
                        "call_start_time": "2025-07-18 10:00:00",
                        "call_end_time": "2025-07-18 10:02:00",
                        "recording_file_path": "/recordings/2025/07/18/call_20250718_100000_1234.wav",
                        "recording_file_name": "call_20250718_100000_1234.wav",
                        "task_batch_id": "BATCH_20250718_001",
                        "creditor": 1,
                        "creditor_name": "某银行股份有限公司",
                        "debtor": 1,
                        "debtor_name": "张三",
                        "call_result_notes": "客户已确认还款计划，约定下周一前完成首期还款"
                    }
                ]
            }
        }
        ```
        """
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        """
        获取语音外呼记录详情

        根据记录ID获取单条语音外呼记录的完整详细信息，包含所有字段的格式化显示、
        关联的债权人和债务人信息，以及通话录音文件等完整数据。

        **请求参数**
        路径参数：
        - id (integer, 必需): 语音外呼记录的唯一标识符

        **请求数据示例**
        该接口为GET请求，无需请求体。

        **响应数据结构**
        ```json
        {
            "code": 200,
            "msg": "success",
            "data": {
                "id": 1,
                "called_number": "13800138000",
                "caller_number": "02088888888",
                "call_status": "connected",
                "call_status_cn": "已接通",
                "call_duration": 120,
                "call_duration_display": "02:00",
                "call_start_time": "2025-07-18 10:00:00",
                "call_end_time": "2025-07-18 10:02:00",
                "recording_file_path": "/recordings/2025/07/18/call_20250718_100000_1234.wav",
                "recording_file_name": "call_20250718_100000_1234.wav",
                "task_batch_id": "BATCH_20250718_001",
                "creditor": 1,
                "creditor_name": "某银行股份有限公司",
                "debtor": 1,
                "debtor_name": "张三",
                "call_result_notes": "客户已确认还款计划，约定下周一前完成首期还款，联系方式已更新"
            }
        }
        ```
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)
