# Custom
upload/
logs/
backups/
celerybeat*

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django stuff:
*.log
*.pot
*.pyc
*.pyo
*.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/
.pytest_cache/
celerybeat-schedule
*.pid

# Translations
*.mo

# Ignore compiled migration files but not the migration scripts themselves
*/migrations/*.pyc
*/migrations/*.pyo
*/migrations/__pycache__/

# Static and media files
/static/
/media/

# Local development environment
.env
.env.*

# OS-generated files
.DS_Store
Thumbs.db

# Editors and IDEs
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Virtual environment
venv/
ENV/
env/
.venv/

# Jupyter Notebook files
.ipynb_checkpoints

# Coverage reports
htmlcov/

# Pyre type checker
.pyre/
