#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : date_helper.py
<AUTHOR> JT_DA
@Date     : 2025/07/22
@File_Desc: 日期处理工具函数 - 提供DataFrame日期列格式化功能
"""

import pandas as pd
import numpy as np
from typing import Union


def format_dataframe_dates(df: pd.DataFrame, date_format: str = '%Y-%m-%d') -> pd.DataFrame:
    """
    对DataFrame中的所有日期类型列进行标准化格式处理
    
    该函数会自动识别DataFrame中的日期类型列（datetime64类型），
    并将其统一格式化为指定的字符串格式，主要用于数据预览功能中确保前端显示的日期格式一致性。
    
    Args:
        df (pd.DataFrame): 需要处理的DataFrame对象
        date_format (str): 日期格式化字符串，默认为'%Y-%m-%d'
    
    Returns:
        pd.DataFrame: 处理后的DataFrame对象，日期列已格式化为字符串
    
    Features:
        - 自动识别datetime64类型的日期列
        - 统一格式化为"YYYY-MM-DD"字符串格式
        - 处理NaT（Not a Time）值，转换为空字符串
        - 异常安全：无法解析的日期值保持原值
        - 不影响非日期类型的列
    
    Example:
        >>> import pandas as pd
        >>> from datetime import datetime
        >>> df = pd.DataFrame({
        ...     'name': ['张三', '李四'],
        ...     'birth_date': [datetime(1990, 1, 1), datetime(1985, 5, 15)],
        ...     'amount': [1000, 2000]
        ... })
        >>> formatted_df = format_dataframe_dates(df)
        >>> print(formatted_df['birth_date'].dtype)  # object (string)
        >>> print(formatted_df['birth_date'].iloc[0])  # '1990-01-01'
    """
    # 创建DataFrame副本，避免修改原始数据
    df_copy = df.copy()
    
    try:
        # 识别所有日期类型的列（包括datetime64的各种变体）
        date_columns = df_copy.select_dtypes(include=['datetime', 'datetime64']).columns.tolist()
        
        # 如果没有日期列，直接返回原DataFrame
        if not date_columns:
            return df_copy
        
        # 对每个日期列进行格式化处理
        for col in date_columns:
            try:
                # 处理日期列的格式化
                df_copy[col] = _format_date_column(df_copy[col], date_format)
                
            except Exception as e:
                # 单个列处理失败时，记录错误但继续处理其他列
                print(f"警告：日期列 '{col}' 格式化失败: {str(e)}")
                continue
        
        return df_copy
        
    except Exception as e:
        # 整体处理失败时，返回原始DataFrame
        print(f"警告：DataFrame日期格式化失败: {str(e)}")
        return df
    

def _format_date_column(series: pd.Series, date_format: str) -> pd.Series:
    """
    格式化单个日期列
    
    Args:
        series (pd.Series): 日期类型的Series对象
        date_format (str): 日期格式化字符串
    
    Returns:
        pd.Series: 格式化后的字符串类型Series
    """
    try:
        # 使用pandas的dt.strftime方法进行格式化
        # 这会自动处理NaT值，将其转换为NaN
        formatted_series = series.dt.strftime(date_format)
        
        # 将NaN值（由NaT转换而来）替换为空字符串
        formatted_series = formatted_series.fillna('')
        
        return formatted_series
        
    except Exception as e:
        # 如果格式化失败，尝试其他方法
        return _fallback_format_date_column(series, date_format)


def _fallback_format_date_column(series: pd.Series, date_format: str) -> pd.Series:
    """
    日期列格式化的备用方法
    
    当标准格式化方法失败时使用，逐个处理每个值以确保异常安全。
    
    Args:
        series (pd.Series): 日期类型的Series对象
        date_format (str): 日期格式化字符串
    
    Returns:
        pd.Series: 格式化后的字符串类型Series
    """
    def format_single_date(value):
        """格式化单个日期值"""
        try:
            # 检查是否为NaT或None
            if pd.isna(value) or value is None:
                return ''
            
            # 如果是pandas的Timestamp对象
            if isinstance(value, pd.Timestamp):
                return value.strftime(date_format)
            
            # 如果是numpy的datetime64对象
            if isinstance(value, np.datetime64):
                # 转换为pandas Timestamp再格式化
                timestamp = pd.Timestamp(value)
                return timestamp.strftime(date_format)
            
            # 如果是Python的datetime对象
            if hasattr(value, 'strftime'):
                return value.strftime(date_format)
            
            # 其他情况，尝试转换为字符串
            return str(value)
            
        except Exception:
            # 无法处理的值，返回原值的字符串形式
            return str(value) if value is not None else ''
    
    # 应用格式化函数到每个值
    return series.apply(format_single_date)


def is_date_column(series: pd.Series) -> bool:
    """
    判断Series是否为日期类型列
    
    Args:
        series (pd.Series): 要检查的Series对象
    
    Returns:
        bool: 如果是日期类型返回True，否则返回False
    """
    try:
        # 检查数据类型
        if pd.api.types.is_datetime64_any_dtype(series):
            return True
        
        # 检查是否可以转换为日期类型（针对字符串日期）
        if series.dtype == 'object':
            # 取非空的前几个值进行测试
            non_null_values = series.dropna().head(5)
            if len(non_null_values) == 0:
                return False
            
            # 尝试解析为日期
            try:
                pd.to_datetime(non_null_values, errors='raise')
                return True
            except (ValueError, TypeError):
                return False
        
        return False
        
    except Exception:
        return False
