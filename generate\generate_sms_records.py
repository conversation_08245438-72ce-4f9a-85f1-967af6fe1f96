#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
短信发送记录测试数据生成脚本
执行方式：python generate_sms_records.py [--count 数量] [--clean]
"""

import os
import sys
import django
import random
import argparse
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction

# 设置Django环境 - 修正路径为项目根目录
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ops_management.settings')
django.setup()

from apps.outbound_communication.models import SmsRecord
from apps.counterparty.models import CreditorBasicInfo, DebtorBasicInfo


class SmsRecordDataGenerator:
    """短信发送记录数据生成器"""
    
    def __init__(self):
        """初始化数据生成器"""
        # 手机号前缀列表 - 中国大陆常用手机号段
        self.mobile_prefixes = [
            '130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
            '150', '151', '152', '153', '155', '156', '157', '158', '159',
            '180', '181', '182', '183', '184', '185', '186', '187', '188', '189',
            '170', '171', '172', '173', '174', '175', '176', '177', '178'
        ]
        
        # 短信内容模板 - 按短信类型分类
        self.sms_content_templates = {
            'notification': [
                '尊敬的客户，您的账单已生成，请及时查看并处理。如有疑问请联系客服。',
                '您好，您的还款计划已更新，请登录系统查看详情。客服热线：************',
                '温馨提醒：您的账户状态已更新，请及时关注相关变化。',
                '通知：您的申请已受理，我们将在3个工作日内处理完成。',
                '系统通知：您的个人信息已更新成功，如非本人操作请及时联系客服。'
            ],
            'verification': [
                '您的验证码是：123456，请在5分钟内输入。如非本人操作，请忽略此短信。',
                '验证码：789012，用于身份验证，请勿泄露给他人。有效期5分钟。',
                '【身份验证】您的动态验证码为：456789，请在10分钟内使用。',
                '安全验证码：321654，请在规定时间内完成验证。如非本人操作请联系客服。'
            ],
            'reminder': [
                '温馨提醒：您的还款日期为明天，请及时处理以免产生逾期费用。',
                '提醒：您有一笔款项即将到期，请提前做好还款准备。',
                '重要提醒：请于本月底前完成相关手续办理，避免影响后续业务。',
                '友情提醒：您的账户余额不足，请及时充值以免影响正常使用。',
                '到期提醒：您的服务即将到期，请及时续费以免中断服务。'
            ],
            'collection': [
                '催收通知：您的欠款已逾期，请尽快联系我们协商还款事宜。联系电话：400-666-6666',
                '重要通知：根据相关法律法规，请您于收到本通知后3日内主动联系处理欠款事宜。',
                '最后通知：您的逾期款项如不及时处理，我们将采取进一步的法律措施。',
                '催收提醒：为维护您的信用记录，请尽快处理逾期款项。我们愿意协商合理的还款方案。',
                '法律告知：您的欠款行为已构成违约，请立即联系我们协商解决方案。'
            ],
            'other': [
                '感谢您选择我们的服务，如有任何问题请随时联系客服。',
                '您的意见对我们很重要，欢迎参与我们的服务满意度调查。',
                '系统维护通知：我们将于今晚22:00-24:00进行系统维护，期间服务可能受影响。',
                '新功能上线：我们推出了新的便民服务功能，欢迎体验使用。',
                '节日祝福：祝您节日快乐，身体健康，工作顺利！'
            ]
        }
        
        # 失败原因模板
        self.failure_reasons = [
            '手机号码不存在或已停机',
            '短信内容包含敏感词汇，被运营商拦截',
            '接收方手机信号不佳，发送失败',
            '短信通道繁忙，发送超时',
            '接收方设置了短信拦截，无法送达',
            '运营商网络异常，发送失败',
            '短信内容格式不符合规范要求',
            '接收方手机存储空间不足，无法接收'
        ]
    
    def generate_mobile_number(self):
        """
        生成随机手机号码
        
        Returns:
            str: 生成的手机号码
        """
        prefix = random.choice(self.mobile_prefixes)
        suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        return prefix + suffix
    
    def generate_task_batch_id(self):
        """
        生成短信任务批次号
        
        Returns:
            str: 任务批次号，格式：SMS_YYYYMMDD_XXX
        """
        # 随机选择日期（最近30天内）
        base_date = timezone.now().date()
        random_days = random.randint(0, 30)
        batch_date = base_date - timedelta(days=random_days)
        
        # 生成批次序号（001-999）
        batch_seq = random.randint(1, 999)
        
        return f"SMS_{batch_date.strftime('%Y%m%d')}_{batch_seq:03d}"
    
    def generate_sms_content(self, sms_type):
        """
        根据短信类型生成短信内容
        
        Args:
            sms_type (str): 短信类型
            
        Returns:
            str: 生成的短信内容
        """
        templates = self.sms_content_templates.get(sms_type, self.sms_content_templates['other'])
        return random.choice(templates)
    
    def generate_send_delivery_times(self, sms_status):
        """
        根据短信状态生成发送和送达时间
        
        Args:
            sms_status (str): 短信状态
            
        Returns:
            tuple: (send_time, delivery_time)
        """
        # 生成发送时间（最近30天内）
        base_time = timezone.now()
        random_days = random.randint(0, 30)
        random_hours = random.randint(8, 20)  # 8-20点发送短信
        random_minutes = random.randint(0, 59)
        random_seconds = random.randint(0, 59)
        
        send_time = base_time.replace(
            hour=random_hours,
            minute=random_minutes,
            second=random_seconds,
            microsecond=0
        ) - timedelta(days=random_days)
        
        # 根据状态生成送达时间
        if sms_status == 'sent_success':
            # 发送成功：送达时间在发送后几秒到几分钟内
            delivery_delay = random.randint(1, 300)  # 1秒到5分钟
            delivery_time = send_time + timedelta(seconds=delivery_delay)
        elif sms_status == 'pending':
            # 待发送：还没有发送时间和送达时间
            send_time = None
            delivery_time = None
        elif sms_status == 'sending':
            # 发送中：有发送时间但没有送达时间
            delivery_time = None
        else:
            # 发送失败：有发送时间但没有送达时间
            delivery_time = None
        
        return send_time, delivery_time
    
    def get_random_creditor_debtor(self):
        """
        随机获取债权人和债务人
        
        Returns:
            tuple: (creditor, debtor) 可能为None
        """
        # 获取现有的债权人和债务人
        creditors = list(CreditorBasicInfo.objects.all())
        debtors = list(DebtorBasicInfo.objects.all())
        
        # 75%概率关联债权人，80%概率关联债务人
        creditor = random.choice(creditors) if creditors and random.random() < 0.75 else None
        debtor = random.choice(debtors) if debtors and random.random() < 0.8 else None
        
        return creditor, debtor
    
    def create_sms_record(self):
        """
        创建单条短信发送记录
        
        Returns:
            SmsRecord: 创建的短信发送记录对象
        """
        # 随机选择短信类型
        sms_type = random.choice([choice[0] for choice in SmsRecord.SMS_TYPE_CHOICES])
        
        # 随机选择发送状态
        sms_status = random.choice([choice[0] for choice in SmsRecord.SMS_STATUS_CHOICES])
        
        # 生成手机号码
        recipient_phone = self.generate_mobile_number()
        
        # 生成短信内容
        sms_content = self.generate_sms_content(sms_type)
        
        # 生成发送和送达时间
        send_time, delivery_time = self.generate_send_delivery_times(sms_status)
        
        # 生成任务批次号（70%概率有批次号）
        task_batch_id = self.generate_task_batch_id() if random.random() < 0.7 else None
        
        # 获取关联的债权人和债务人
        creditor, debtor = self.get_random_creditor_debtor()
        
        # 生成失败原因（仅在发送失败时）
        failure_reason = None
        if sms_status == 'sent_failed':
            failure_reason = random.choice(self.failure_reasons)
        
        # 创建短信发送记录
        sms_record = SmsRecord(
            recipient_phone=recipient_phone,
            sms_content=sms_content,
            sms_status=sms_status,
            sms_type=sms_type,
            send_time=send_time,
            delivery_time=delivery_time,
            task_batch_id=task_batch_id,
            creditor=creditor,
            debtor=debtor,
            failure_reason=failure_reason,
            created_by=1,  # 假设系统用户ID为1
            updated_by=1
        )
        
        return sms_record
    
    def generate_test_data(self, count=20, clean_existing=False):
        """
        生成指定数量的测试数据
        
        Args:
            count (int): 生成数据数量
            clean_existing (bool): 是否清理现有测试数据
        """
        print(f"开始生成 {count} 条短信发送记录测试数据...")
        
        if clean_existing:
            print("正在清理现有测试数据...")
            # 可以根据需要添加清理逻辑，比如删除包含特定标识的测试数据
            # SmsRecord.objects.filter(sms_content__contains='测试数据').delete()
        
        created_count = 0
        sms_records = []
        
        try:
            with transaction.atomic():  # 使用数据库事务确保数据一致性
                for i in range(count):
                    try:
                        sms_record = self.create_sms_record()
                        sms_records.append(sms_record)
                        created_count += 1
                        
                        # 显示进度
                        type_display = sms_record.get_sms_type_display()
                        status_display = sms_record.get_sms_status_display()
                        print(f"✓ 已生成第 {created_count} 条记录: {sms_record.recipient_phone} - {type_display} ({status_display})")
                        
                    except Exception as e:
                        print(f"✗ 生成第 {i+1} 条记录时出错: {str(e)}")
                        continue
                
                # 批量创建数据
                SmsRecord.objects.bulk_create(sms_records)
                print(f"\n🎉 数据生成完成！成功创建 {created_count} 条短信发送记录")
                
                # 显示统计信息
                self.show_statistics()
                
        except Exception as e:
            print(f"❌ 数据生成过程中发生错误: {str(e)}")
            return False
        
        return True
    
    def show_statistics(self):
        """显示数据统计信息"""
        total_count = SmsRecord.objects.count()
        print(f"\n📊 数据库统计信息:")
        print(f"短信发送记录总数: {total_count}")
        
        # 按发送状态统计
        print("\n按发送状态统计:")
        for status_code, status_name in SmsRecord.SMS_STATUS_CHOICES:
            count = SmsRecord.objects.filter(sms_status=status_code).count()
            print(f"{status_name}: {count} 条")
        
        # 按短信类型统计
        print("\n按短信类型统计:")
        for type_code, type_name in SmsRecord.SMS_TYPE_CHOICES:
            count = SmsRecord.objects.filter(sms_type=type_code).count()
            print(f"{type_name}: {count} 条")


def main():
    """主函数 - 处理命令行参数并执行数据生成"""
    parser = argparse.ArgumentParser(description='生成短信发送记录测试数据')
    parser.add_argument('--count', type=int, default=20, help='生成数据数量（默认20条）')
    parser.add_argument('--clean', action='store_true', help='清理现有测试数据')
    
    args = parser.parse_args()
    
    # 创建数据生成器并执行
    generator = SmsRecordDataGenerator()
    success = generator.generate_test_data(count=args.count, clean_existing=args.clean)
    
    if success:
        print("\n✅ 短信发送记录测试数据生成完成！")
    else:
        print("\n❌ 数据生成失败，请检查错误信息")
        sys.exit(1)


if __name__ == '__main__':
    main()
