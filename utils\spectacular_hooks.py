#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : spectacular_hooks.py
<AUTHOR> JT_DA
@Date     : 2025/07/29
@File_Desc: drf-spectacular自定义钩子函数，实现基于URL路由的多级菜单自动生成功能
"""

from django.apps import apps


def postprocess_schema_tags(result, generator, request, public):
    """
    drf-spectacular后处理钩子：基于URL路径重新生成多级标签结构

    直接在后处理阶段分析所有路径，生成层级标签，确保多级菜单正确显示

    Args:
        result (dict): 生成的OpenAPI schema
        generator: schema生成器实例
        request: HTTP请求对象
        public (bool): 是否为公开schema

    Returns:
        dict: 处理后的schema
    """

    def get_app_verbose_name(app_name):
        """
        动态获取Django应用的verbose_name

        Args:
            app_name (str): 应用名称（如 'mediation_management'）

        Returns:
            str: 应用的中文名称（verbose_name）或格式化的应用名称
        """
        try:
            # 构建完整的应用名称（加上apps.前缀）
            full_app_name = f"apps.{app_name}"
            app_config = apps.get_app_config(full_app_name)
            if hasattr(app_config, 'verbose_name') and app_config.verbose_name:
                return app_config.verbose_name
        except LookupError:
            # 如果应用不存在，尝试不带前缀的名称
            try:
                app_config = apps.get_app_config(app_name)
                if hasattr(app_config, 'verbose_name') and app_config.verbose_name:
                    return app_config.verbose_name
            except LookupError:
                pass

        # 备用映射配置（当无法获取verbose_name时使用）
        fallback_mapping = {
            'mediation_management': '调解管理',
            'data_governance': '数据治理',
            'counterparty': '人员管理（债权人/债务人）',
            'outbound_communication': '对外通信',
            'case_display': '案例展示',
            'feedback': '投诉建议',
            'user': '用户管理',
            'wechat': '微信应用',
        }

        return fallback_mapping.get(app_name, app_name.replace('_', ' ').title())

    # 资源名称到中文名称的映射配置
    RESOURCE_NAME_MAPPING = {
        # 调解管理模块
        'mediation_case': '调解案件',
        'mediation_plan': '调解方案',

        # 数据治理模块
        'asset_package_management': '资产包管理',
        'asset_package_field_config': '字段配置',

        # 对手方管理模块
        'creditor_basic_info': '债权人信息',
        'debtor_basic_info': '债务人信息',

        # 对外通信模块
        'voice_call_records': '语音外呼记录',
        'sms_records': '短信记录',

        # 案例展示模块
        'case_display': '案例展示',

        # 投诉建议模块
        'feedback': '投诉建议',

        # 用户管理模块
        'mediators': '调解员管理',
        'user_info': '用户信息',
        'operation_log': '操作日志',

        # 微信应用模块
        'faceid': '人脸核身',
    }

    def extract_path_components(path):
        """从URL路径中提取应用名称和资源名称"""
        path_parts = path.strip('/').split('/')

        if len(path_parts) >= 2:
            app_name = path_parts[0]
            resource_name = path_parts[1]
            return app_name, resource_name
        elif len(path_parts) == 1:
            app_name = path_parts[0]
            return app_name, None
        else:
            return None, None

    def generate_tag(app_name, resource_name):
        """根据应用名称和资源名称生成标签"""
        # 生成父级标签（应用级别） - 使用Django应用的verbose_name
        parent_tag = get_app_verbose_name(app_name)

        # 生成子级标签（资源级别）
        if resource_name:
            child_tag = RESOURCE_NAME_MAPPING.get(resource_name, resource_name.replace('_', ' ').title())
            # 组合标签格式：父级 > 子级
            return f"{parent_tag} > {child_tag}"
        else:
            return parent_tag

    # 收集所有使用的标签
    used_tags = set()

    # 遍历所有路径和操作，重新生成标签
    for path, path_info in result.get('paths', {}).items():
        # 跳过非API路径
        if any(skip_path in path for skip_path in ['/admin/', '/schema/', '/static/']):
            continue

        # 提取路径组件
        app_name, resource_name = extract_path_components(path)

        if app_name:
            # 生成新标签
            new_tag = generate_tag(app_name, resource_name)

            # 为该路径下的所有操作设置新标签
            for method, operation in path_info.items():
                if isinstance(operation, dict):
                    operation['tags'] = [new_tag]
                    used_tags.add(new_tag)

    # 生成标签定义，包含中文描述
    tag_definitions = []

    # 按标签名称排序，确保层级结构清晰
    sorted_tags = sorted(used_tags)

    for tag in sorted_tags:
        tag_def = {'name': tag}

        # 为标签添加描述
        if '>' in tag:
            # 子级标签
            parent, child = tag.split(' > ', 1)
            tag_def['description'] = f'{parent}模块下的{child}相关接口'
        else:
            # 父级标签
            tag_def['description'] = f'{tag}模块相关接口'

        tag_definitions.append(tag_def)

    # 更新schema中的标签定义
    result['tags'] = tag_definitions

    return result
