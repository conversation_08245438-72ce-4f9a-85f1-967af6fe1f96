# Generated by Django 4.1.13 on 2025-07-22 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SystemGroup',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False, verbose_name='部门id')),
                ('name', models.CharField(blank=True, max_length=255, null=True, verbose_name='部门名称')),
            ],
            options={
                'verbose_name': '用户部门表',
                'db_table': 't_sys_auth_group',
            },
        ),
        migrations.CreateModel(
            name='SystemUser',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False, verbose_name='用户id')),
                ('username', models.CharField(max_length=255, unique=True, verbose_name='唯一标识')),
                ('is_superuser', models.BooleanField(null=True, verbose_name='')),
                ('is_staff', models.BooleanField(null=True, verbose_name='')),
                ('is_active', models.BooleanField(null=True, verbose_name='')),
                ('email', models.CharField(blank=True, max_length=50, null=True, verbose_name='邮件地址')),
                ('phone_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='手机号码')),
                ('id_card_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='身份证号码')),
                ('app_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='应用编号')),
                ('group_id', models.IntegerField(null=True, verbose_name='部门id')),
                ('group_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='部门名称')),
                ('role_id', models.IntegerField(null=True, verbose_name='角色id')),
                ('role_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='角色名称')),
            ],
            options={
                'verbose_name': '用户信息表',
                'db_table': 't_sys_auth_user',
            },
        ),
    ]
