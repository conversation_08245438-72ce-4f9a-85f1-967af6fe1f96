#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : asset_package_management_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/07/15
@File_Desc: 资产包管理视图集
"""

import logging
from django.db import transaction
from django.utils import timezone
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend

from apps.data_governance.models import AssetPackageManagement, AssetPackageFieldMapping
from apps.data_governance.serializers.asset_package_management_serializer import (
    AssetPackageManagementListSerializer,
    AssetPackageManagementCreateSerializer,
    AssetPackageManagementUpdateSerializer,
    AssetPackageManagementAttachmentUpdateSerializer,
    AssetPackageExpressionCalculationSerializer
)
from apps.user.models import SystemUser
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult
from utils.expression_calculator import calculate_expression_with_asset_data

# 获取日志记录器
logger = logging.getLogger(__name__)


class BaseAssetPackageManagementViewSet(viewsets.ModelViewSet):
    """
    资产包管理基础视图集

    提供资产包管理的基础功能配置，包括查询集、序列化器、分页和过滤配置。
    该基础类定义了资产包管理的通用操作规范和数据访问模式。
    """
    queryset = AssetPackageManagement.objects.all().order_by("-created_time")
    serializer_class = AssetPackageManagementListSerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["creditor", "uploader", "package_status"]  # 支持按债权人、上传人和资产包状态过滤
    search_fields = ["package_name"]  # 支持按资产包名称搜索


class AssetPackageManagementViewSet(BaseAssetPackageManagementViewSet):
    """
    资产包管理视图集

    提供资产包管理的完整业务功能，包括标准CRUD操作和专业的资产包处理功能。
    该视图集是资产包管理系统的核心组件，负责Excel文件处理、字段映射管理和数据验证。
    """

    def get_serializer_class(self):
        """
        根据不同的操作动态选择合适的序列化器
        """
        if self.action == 'create':
            return AssetPackageManagementCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return AssetPackageManagementUpdateSerializer
        else:
            return AssetPackageManagementListSerializer
    
    def list(self, request, *args, **kwargs):
        """
        获取资产包管理列表

        获取系统中所有资产包的分页列表，支持多维度过滤和搜索功能。
        该接口为资产包管理提供完整的数据展示和业务查询支持。

        **请求参数：**
        - creditor (整数, 可选): 债权人ID，用于过滤指定债权人的资产包
        - uploader (整数, 可选): 上传人ID，用于过滤指定用户上传的资产包
        - package_status (字符串, 可选): 资产包状态，可选值：available(可用)、unavailable(不可用)
        - search (字符串, 可选): 按资产包名称进行模糊搜索的关键词
        - page (整数, 可选): 页码，从1开始，默认为1
        - page_size (整数, 可选): 每页记录数，默认20，最大100

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "count": 100,
                "next": "http://example.com/api/next_page",
                "previous": "http://example.com/api/previous_page",
                "results": [
                    {
                        "id": 1,
                        "package_name": "2025年第一批资产包",
                        "source_file": "/upload/asset_packages/file.xlsx",
                        "source_file_name": "客户数据.xlsx",
                        "file_size": 1024000,
                        "file_size_display": "1.0 MB",
                        "uploader": 1,
                        "uploader_name": "张三",
                        "upload_time": "2025-07-17 10:30:00",
                        "creditor": 1,
                        "creditor_name": "某银行股份有限公司",
                        "package_status": "available",
                        "package_status_cn": "可用",
                        "unavailable_reason": null,
                        "mapping_count": 15,
                        "field_mappings_detail": [
                            {
                                "original_field_name": "客户姓名",
                                "mapped_field_config": {
                                    "field_name": "客户姓名",
                                    "field_type": "text",
                                    "is_masked": true
                                }
                            }
                        ],
                        "mapped_field_names": ["客户姓名", "联系电话", "身份证号"],
                        "attachments": [
                            {
                                "id": 1,
                                "file_name": "补充材料.pdf",
                                "file": "/upload/attachments/supplement.pdf"
                            }
                        ],
                        "attachments_count": 1,
                        "file_cn": "补充材料.pdf",
                        "mediation_config": {
                            "mediator_name": "李四",
                            "mediation_date": "2025-01-15"
                        },
                        "created_time": "2025-07-17 10:30:00",
                        "updated_time": "2025-07-17 10:30:00"
                    }
                ]
            }
        }
        ```
        """
        return super().list(request, *args, **kwargs)
    
    def create(self, request, *args, **kwargs):
        """
        创建新的资产包

        创建一个新的资产包，包括Excel文件上传、表头解析和字段映射自动创建。
        该接口是资产包管理的核心功能，负责完整的文件处理和数据初始化流程。

        **请求参数：**
        - package_name (字符串, 必需): 资产包名称，用于业务标识，最大长度255字符
        - file (文件, 必需): Excel文件，支持.xlsx和.xls格式，最大50MB
        - creditor (整数, 可选): 债权人ID，关联的债权人信息，可为空

        **请求数据示例：**
        ```
        Content-Type: multipart/form-data

        package_name: "2025年第一批资产包"
        file: [Excel文件二进制数据]
        creditor: 1
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "资产包创建成功",
            "data": {
                "id": 1,
                "package_name": "2025年第一批资产包",
                "mapping_count": 15
            }
        }
        ```
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():  # 使用数据库事务确保数据一致性
            # 设置上传人为当前用户对应的SystemUser实例（如果有认证）
            if hasattr(request, 'user') and request.user.is_authenticated:
                try:
                    # 根据当前用户的username查询SystemUser模型
                    system_user = SystemUser.objects.get(username=request.user.username)
                    serializer.validated_data['uploader'] = system_user
                except SystemUser.DoesNotExist:
                    # 如果SystemUser不存在，记录警告但不阻止创建操作
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"SystemUser with username '{request.user.username}' not found")
                    # uploader字段允许为null，所以可以继续创建
                    serializer.validated_data['uploader'] = None

            asset_package = serializer.save()

        return AjaxResult.success(
            msg="资产包创建成功",
            data={
                'id': asset_package.id,
                'package_name': asset_package.package_name,
                'mapping_count': asset_package.field_mappings.count()
            }
        )
    
    def retrieve(self, request, *args, **kwargs):
        """
        获取资产包详细信息

        根据资产包ID获取完整的详细信息，包括基本信息、文件信息、关联数据和字段映射配置。
        该接口提供资产包的全面视图，用于详情页面展示和编辑操作的数据加载。

        **请求参数：**
        - id (整数, 必需): 资产包的唯一标识ID，通过URL路径传递

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "id": 1,
                "package_name": "2025年第一批资产包",
                "source_file": "/upload/asset_packages/file.xlsx",
                "source_file_name": "客户数据.xlsx",
                "file_size": 2048000,
                "file_size_display": "2.0 MB",
                "uploader": 1,
                "uploader_name": "张三",
                "upload_time": "2025-07-17 10:30:00",
                "creditor": 1,
                "creditor_name": "某银行股份有限公司",
                "package_status": "available",
                "package_status_cn": "可用",
                "unavailable_reason": null,
                "mapping_count": 15,
                "field_mappings_detail": [
                    {
                        "id": 1,
                        "original_field_name": "客户姓名",
                        "mapped_field_config": {
                            "id": 1,
                            "field_name": "客户姓名",
                            "field_type": "text",
                            "field_type_cn": "文本类型",
                            "data_validation": "none",
                            "data_validation_cn": "无校验",
                            "is_masked": true,
                            "prefix_keep_chars": 1,
                            "suffix_keep_chars": 1,
                            "display_order": 100
                        }
                    }
                ],
                "mapped_field_names": ["客户姓名", "联系电话", "身份证号"],
                "created_time": "2025-07-17 10:30:00",
                "updated_time": "2025-07-17 11:00:00"
            }
        }
        ```
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)
    
    def update(self, request, *args, **kwargs):
        """
        更新资产包信息

        更新现有资产包的基本信息和字段映射关系。该接口支持资产包元数据的修改
        和Excel字段与标准字段配置之间映射关系的调整。

        **请求参数：**
        - id (整数, 必需): 资产包的唯一标识ID，通过URL路径传递
        - package_name (字符串, 可选): 资产包名称，更新业务标识名称
        - creditor (整数, 可选): 债权人ID，更新关联的债权人信息，可为null
        - upload_time (日期时间, 可选): 上传时间，手动调整上传时间
        - field_mappings (数组, 可选): 字段映射数组，每个记录包含：
          - id (整数, 可选): 映射记录ID，用于更新现有映射
          - original_field_name (字符串, 必需): Excel表头字段名称
          - mapped_field_config_id (整数, 可选): 标准字段配置ID，可为null
          - mapped_debtor_field_config (字符串, 可选): 债务人字段配置，可选值：debtor_type、debtor_name、id_type、id_number、phones、emails、addresses、wechats

        **请求数据示例：**
        ```json
        {
            "package_name": "2025年第一批资产包（更新）",
            "creditor": 2,
            "field_mappings": [
                {
                    "id": 1,
                    "original_field_name": "客户姓名",
                    "mapped_field_config_id": 5,
                    "mapped_debtor_field_config": "debtor_name"
                },
                {
                    "id": 2,
                    "original_field_name": "联系电话",
                    "mapped_field_config_id": 3,
                    "mapped_debtor_field_config": "phones"
                },
                {
                    "id": 3,
                    "original_field_name": "身份证号",
                    "mapped_field_config_id": 8,
                    "mapped_debtor_field_config": "id_number"
                }
            ]
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "编辑成功",
            "data": {
                "id": 1,
                "package_name": "2025年第一批资产包（更新）",
                "package_status": "available",
                "package_status_cn": "可用",
                "unavailable_reason": null,
                "mapping_count": 14
            }
        }
        ```
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():  # 使用数据库事务确保数据一致性
            # 保存更新，包括基本信息和字段映射关系
            updated_instance = serializer.save()

        # 返回更新后的统计信息，包含状态信息
        return AjaxResult.success(
            msg="编辑成功",
            data={
                'id': updated_instance.id,
                'package_name': updated_instance.package_name,
                'package_status': updated_instance.package_status,
                'package_status_cn': updated_instance.get_package_status_display(),
                'unavailable_reason': updated_instance.unavailable_reason,
                'mapping_count': updated_instance.field_mappings.count()
            }
        )

    def partial_update(self, request, *args, **kwargs):
        """
        禁用部分更新操作

        系统不支持PATCH方法的部分更新操作，以确保数据完整性和业务逻辑一致性。
        请使用PUT方法进行完整更新操作。

        **请求参数：**
        - 任何PATCH请求参数都将被拒绝

        **响应数据结构：**
        ```json
        {
            "code": 405,
            "msg": "不支持部分更新操作，请使用PUT方法进行完整更新",
            "data": null
        }
        ```
        """
        from rest_framework.response import Response
        from rest_framework import status

        return Response(
            {
                "code": 405,
                "msg": "不支持部分更新操作，请使用PUT方法进行完整更新",
                "data": None
            },
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )

    def destroy(self, request, *args, **kwargs):
        """
        删除资产包

        删除指定的资产包及其所有关联数据，包括文件、映射关系等。
        该操作是不可逆的，会彻底清除资产包的所有相关信息。

        **请求参数：**
        - id (整数, 必需): 资产包的唯一标识ID，通过URL路径传递

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "资产包 '2025年第一批资产包' 删除成功",
            "data": null
        }
        ```
        """
        instance = self.get_object()
        package_name = instance.package_name

        with transaction.atomic():  # 使用数据库事务确保删除操作的原子性
            # 删除关联的字段映射记录
            instance.field_mappings.clear()
            # 删除资产包记录
            instance.delete()

        return AjaxResult.success(msg=f"资产包 '{package_name}' 删除成功")
    

    
    @action(detail=True, methods=['post'])
    def reprocess_excel(self, request, pk=None):
        """
        重新上传Excel文件并处理字段映射关系

        为现有资产包重新上传Excel文件，支持两种处理模式：完全重置或保留映射。
        该功能用于文件更新、数据修正或字段映射重新配置等场景。

        **请求参数：**
        - id (整数, 必需): 资产包的唯一标识ID，通过URL路径传递
        - file (文件, 必需): 新的Excel文件，支持.xlsx和.xls格式，最大50MB
        - reset_mappings (字符串, 可选): 重置映射标志，可选值：true(重置字段映射关系)、false(保留现有字段映射关系)，默认为true

        **请求数据示例：**
        ```
        Content-Type: multipart/form-data

        file: [Excel文件二进制数据]
        reset_mappings: "true"
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "Excel文件重新上传成功",
            "data": {
                "id": 1,
                "package_name": "2025年第一批资产包",
                "package_status": "available",
                "package_status_cn": "可用",
                "unavailable_reason": null,
                "mapping_count": 15
            }
        }
        ```
        """
        asset_package = self.get_object()

        # 验证必需参数
        if 'file' not in request.FILES:
            return AjaxResult.fail(msg="请上传新的Excel文件")

        file = request.FILES['file']
        reset_mappings = request.data.get('reset_mappings', 'true').lower() == 'true'

        # 验证文件格式和大小
        try:
            create_serializer = AssetPackageManagementCreateSerializer()
            validated_file = create_serializer.validate_file(file)
        except Exception as e:
            return AjaxResult.fail(msg=f"文件验证失败: {str(e)}")

        try:
            with transaction.atomic():
                # 更新文件信息
                asset_package.source_file = validated_file
                asset_package.file_size = validated_file.size
                asset_package.upload_time = timezone.now()  # 更新上传时间为当前时间

                # 设置上传人为当前用户对应的SystemUser实例（如果有认证）
                if hasattr(request, 'user') and request.user.is_authenticated:
                    try:
                        # 根据当前用户的username查询SystemUser模型
                        system_user = SystemUser.objects.get(username=request.user.username)
                        asset_package.uploader = system_user
                    except SystemUser.DoesNotExist:
                        # 如果SystemUser不存在，记录警告但不阻止上传操作
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.warning(f"SystemUser with username '{request.user.username}' not found")
                        # uploader字段允许为null，所以可以继续处理
                        asset_package.uploader = None

                asset_package.save()

                # 根据reset_mappings参数决定处理方式
                if reset_mappings:
                    # 清除现有的字段映射关系
                    asset_package.field_mappings.clear()

                    # 重新处理Excel文件，创建新的字段映射
                    create_serializer._process_excel_file(asset_package, validated_file)

                # 执行数据验证并更新资产包状态
                update_serializer = AssetPackageManagementUpdateSerializer()
                update_serializer._validate_excel_data_and_update_status(asset_package)

                # 刷新实例以获取最新状态
                asset_package.refresh_from_db()

            return AjaxResult.success(
                msg="Excel文件重新上传成功",
                data={
                    'id': asset_package.id,
                    'package_name': asset_package.package_name,
                    'package_status': asset_package.package_status,
                    'package_status_cn': asset_package.get_package_status_display(),
                    'unavailable_reason': asset_package.unavailable_reason,
                    'mapping_count': asset_package.field_mappings.count()
                }
            )

        except Exception as e:
            return AjaxResult.fail(msg=f"文件处理失败: {str(e)}")
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """
        获取资产包管理统计信息

        提供资产包管理系统的全局统计数据，用于数据分析、报表展示和系统监控。
        该接口实时计算各种统计指标，为管理决策提供数据支持。

        **请求参数：**
        - 无需任何参数

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "操作成功",
            "data": {
                "total_packages": 150,
                "total_mappings": 2250,
                "status_distribution": {
                    "可用": 120,
                    "不可用": 30
                },
                "creditor_distribution": {
                    "某银行股份有限公司": 45,
                    "某信托有限公司": 38,
                    "某资产管理公司": 32,
                    "未指定债权人": 35
                }
            }
        }
        ```
        """
        # 统计资产包总数量
        total_packages = AssetPackageManagement.objects.count()

        # 统计字段映射总数量
        total_mappings = sum(
            package.field_mappings.count()
            for package in AssetPackageManagement.objects.all()
        )

        # 按状态统计资产包分布
        status_stats = {}
        for package in AssetPackageManagement.objects.all():
            status_display = package.get_package_status_display()
            if status_display not in status_stats:
                status_stats[status_display] = 0
            status_stats[status_display] += 1

        # 按债权人统计资产包分布
        creditor_stats = {}
        for package in AssetPackageManagement.objects.select_related('creditor'):
            creditor_name = package.creditor.creditor_name if package.creditor else "未指定债权人"
            if creditor_name not in creditor_stats:
                creditor_stats[creditor_name] = 0
            creditor_stats[creditor_name] += 1

        return AjaxResult.success(
            data={
                'total_packages': total_packages,
                'total_mappings': total_mappings,
                'status_distribution': status_stats,
                'creditor_distribution': creditor_stats
            }
        )

    @action(detail=False, methods=['get'])
    def debtor_field_choices(self, request):
        """
        获取债务人字段配置选择项

        返回债务人字段配置的所有可选项，用于前端字段映射配置时的下拉选择。
        该接口提供标准化的债务人字段选项，确保字段映射配置的一致性和准确性。

        **请求参数：**
        - 无需任何参数

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "获取成功",
            "data": [
                {"value": "debtor_type", "label": "债务人类型"},
                {"value": "debtor_name", "label": "债务人名称"},
                {"value": "id_type", "label": "证件类型"},
                {"value": "id_number", "label": "证件号码"},
                {"value": "phones", "label": "联系方式-电话"},
                {"value": "emails", "label": "联系方式-邮箱"},
                {"value": "addresses", "label": "联系方式-地址"},
                {"value": "wechats", "label": "联系方式-微信"}
            ]
        }
        ```
        """
        try:
            # 获取债务人字段配置选择项
            choices = AssetPackageFieldMapping.DEBTOR_FIELD_CHOICES

            # 转换为前端需要的格式：[{"value": "field_name", "label": "display_name"}]
            choices_data = [
                {"value": choice[0], "label": choice[1]}
                for choice in choices
            ]

            return AjaxResult.success(
                msg="获取成功",
                data=choices_data
            )

        except Exception as e:
            return AjaxResult.fail(msg=f"获取债务人字段选择项失败: {str(e)}")

    @action(detail=True, methods=['post'])
    def update_attachments_and_mediation(self, request, pk=None):
        """
        更新资产包的附件文件和调解配置信息

        专门用于更新资产包的附件文件和调解信息配置的接口。
        支持附件文件的新增、删除操作和调解配置的JSON数据更新。

        **请求参数：**
        - id (整数, 必需): 资产包的唯一标识ID，通过URL路径传递
        - mediation_config (JSON, 可选): 调解信息配置，JSON格式的配置数据
        - file (文件列表, 可选): 新上传的附件文件列表，支持多文件上传，单个文件最大10MB
        - file_id (整数列表, 可选): 需要保留的现有附件ID列表，未包含的附件将被移除

        **请求数据示例：**
        ```
        Content-Type: multipart/form-data

        mediation_config: {"mediator_name": "张三", "mediation_date": "2025-01-15"}
        file: [附件文件1二进制数据]
        file: [附件文件2二进制数据]
        file_id: [1, 3, 5]
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "附件和调解配置更新成功",
            "data": {
                "id": 123,
                "mediation_config": {"mediator_name": "张三", "mediation_date": "2025-01-15"},
                "attachments_count": 5,
                "updated_time": "2025-01-15 14:30:00"
            }
        }
        ```
        """
        try:
            # 获取资产包实例
            instance = self.get_object()

            # 使用专用序列化器进行数据验证和更新
            serializer = AssetPackageManagementAttachmentUpdateSerializer(
                instance,
                data=request.data,
                partial=True
            )

            if serializer.is_valid():
                # 执行更新操作
                updated_instance = serializer.save()

                # 构建响应数据
                response_data = {
                    'id': updated_instance.id,
                    'mediation_config': updated_instance.mediation_config,
                    'attachments_count': updated_instance.attachments.count(),
                    'updated_time': updated_instance.updated_time.strftime('%Y-%m-%d %H:%M:%S') if updated_instance.updated_time else None
                }

                return AjaxResult.success(
                    msg="附件和调解配置更新成功",
                    data=response_data
                )
            else:
                # 提取验证错误信息
                error_messages = []
                for field, errors in serializer.errors.items():
                    if isinstance(errors, list):
                        error_messages.extend([str(error) for error in errors])
                    else:
                        error_messages.append(str(errors))

                return AjaxResult.fail(
                    msg=f"更新失败：{'; '.join(error_messages)}"
                )

        except AssetPackageManagement.DoesNotExist:
            return AjaxResult.fail(msg="指定的资产包不存在")
        except Exception as e:
            return AjaxResult.fail(msg=f"更新附件和调解配置失败: {str(e)}")

    @action(detail=False, methods=['post'])
    def expression_calculation(self, request):
        """
        资产包表达式计算接口

        提供基于资产包数据的表达式计算功能，支持文本格式化和数学运算两种处理模式。
        该接口是资产包数据处理的核心组件，负责动态表达式解析、字段映射和结果计算。

        **请求参数：**
        - asset_package_id (整数, 可选): 资产包ID，用于查询对应的资产包对象和数据文件
        - mediation_case_id (整数, 可选): 调解案件ID，用于查询关联的资产包对象和数据文件
        - asset_package_row_number (整数, 可选): 资产包行号，默认为1，指定计算数据的行索引
        - expression (字符串, 必需): 计算表达式，使用花括号{}包围变量名，如："{债权总额}-{本金余额}"
        - logic_type (字符串, 必需): 逻辑处理类型，可选值：
          - text_format: 文本格式化模式，仅进行变量替换，返回格式化后的字符串
          - result_calculation: 结果运算模式，进行变量替换后使用数学计算，返回运算结果

        **注意：** asset_package_id 和 mediation_case_id 参数不能同时传入，必须传入其中一个。

        **请求数据示例：**
        ```json
        {
            "asset_package_id": 1,
            "asset_package_row_number": 2,
            "expression": "{债权总额（折人民币）}-{本金余额（折人民币）}",
            "logic_type": "result_calculation"
        }
        ```

        或者：

        ```json
        {
            "mediation_case_id": 123,
            "asset_package_row_number": 2,
            "expression": "{债权总额（折人民币）}-{本金余额（折人民币）}",
            "logic_type": "result_calculation"
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "计算成功",
            "state": "success",
            "data": "150000.00（仅示例，数据来源：资产包第2行）"
        }
        ```
        """
        try:
            # 使用序列化器验证请求数据
            serializer = AssetPackageExpressionCalculationSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # 获取验证后的数据
            validated_data = serializer.validated_data
            asset_package_id = validated_data.get("asset_package_id")
            mediation_case_id = validated_data.get("mediation_case_id")
            row_number = validated_data["asset_package_row_number"]
            expression = validated_data["expression"]
            logic_type = validated_data["logic_type"]

            # 根据参数类型获取资产包对象
            if asset_package_id is not None:
                # 通过资产包ID直接查询
                asset_package = AssetPackageManagement.objects.get(id=asset_package_id)
                logger.info(
                    f"开始处理资产包表达式计算，资产包ID: {asset_package_id}, 行号: {row_number}, 逻辑类型: {logic_type}"
                )
            else:
                # 通过调解案件ID查询关联的资产包
                from apps.mediation_management.models import MediationCase
                mediation_case = MediationCase.objects.get(id=mediation_case_id)

                if not mediation_case.asset_package:
                    return AjaxResult.fail(msg="指定的调解案件未关联资产包")

                asset_package = mediation_case.asset_package
                logger.info(
                    f"开始处理资产包表达式计算，调解案件ID: {mediation_case_id}, 资产包ID: {asset_package.id}, 行号: {row_number}, 逻辑类型: {logic_type}"
                )

            # 使用工具函数计算表达式
            calculation_result = calculate_expression_with_asset_data(
                asset_package=asset_package, row_number=row_number, expression=expression, logic_type=logic_type
            )

            # 检查计算结果
            if calculation_result["success"]:
                logger.info(f"表达式计算成功，结果: {calculation_result['result']}")
                return AjaxResult.success(
                    msg="计算成功",
                    data=str(calculation_result["result"]) + f"（仅示例，数据来源资产包第{row_number}行）"
                )
            else:
                logger.error(f"表达式计算失败: {calculation_result['error']}")
                return AjaxResult.fail(msg=f"计算失败: {calculation_result['error']}")

        except AssetPackageManagement.DoesNotExist:
            logger.error(f"资产包不存在，ID: {asset_package_id}")
            return AjaxResult.fail(msg="指定的资产包不存在")

        except Exception as e:
            # 导入MediationCase异常处理
            try:
                from apps.mediation_management.models import MediationCase
                if isinstance(e, MediationCase.DoesNotExist):
                    logger.error(f"调解案件不存在，ID: {mediation_case_id}")
                    return AjaxResult.fail(msg="指定的调解案件不存在")
            except:
                pass

            logger.error(f"表达式计算异常: {str(e)}")
            return AjaxResult.fail(msg=f"计算异常: {str(e)}")
